@import '@fontsource-variable/playfair-display';
@import '@fontsource/poppins';
@import 'tailwindcss';

@theme {
	--font-sans: "Poppins", system-ui, sans-serif;
	--font-serif: "Playfair Display Variable", serif;
}

@plugin 'daisyui' {
	themes: dark --default;
}
@plugin "@tailwindcss/typography";
@plugin 'tailwind-scrollbar';

@layer base {
	html {
		font-family: var(--font-sans);
		scroll-behavior: smooth;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		font-family: var(--font-serif);
		font-weight: 600;
	}

	button {
		@apply btn btn-md lg:btn-lg xl:btn-xl font-light;
	}

	li {
		font-family: var(--font-sans);
		text-transform: uppercase;
	}

	/* Convert action buttons to proper buttons without underlines */
	.btn {
		text-decoration: none;
	}
}

@layer utilities {
	.drawer-open {
		overflow: hidden;
		width: 100%;
	}

	/* Prevent layout shift when drawer opens */

	/* Fix for iOS */
	@supports (-webkit-touch-callout: none) {
		.drawer-side {
			height: -webkit-fill-available;
		}
	}
}
