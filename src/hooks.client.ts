import {
	handleErrorWithSentry,
	replayIntegration,
	browserTracingIntegration,
	httpContextIntegration
} from '@sentry/sveltekit';
import * as Sentry from '@sentry/sveltekit';

Sentry.init({
	dsn: 'https://<EMAIL>/4509055809748992',

	// Set environment based on build mode
	environment: import.meta.env.DEV ? 'development' : 'production',


	// Performance monitoring sample rate in production
	// Consider adjusting this in production to avoid excessive data
	tracesSampleRate: import.meta.env.DEV ? 1.0 : 0.1,

	// Session replay settings - adjust these as needed
	replaysSessionSampleRate: import.meta.env.DEV ? 1.0 : 0.1, // Sample 10% of sessions in production
	replaysOnErrorSampleRate: 1.0, // Record all sessions that have errors

	// Error filtering
	// Ignore errors from ad blockers, extensions, etc.
	ignoreErrors: [
		'Network request failed',
		'top.GLOBALS',
		'ResizeObserver loop limit exceeded',
		/^Script error\.?$/,
		/No network connection/i
	],

	// Custom context in all events
	initialScope: {
		tags: {
			app: 'arisetransit-frontend',
			framework: 'sveltekit'
		}
	},

	// Better debugging in development
	debug: import.meta.env.DEV,

	// Integrations
	integrations: [
		// Session replay for debugging user issues
		replayIntegration({
			maskAllText: false,
			blockAllMedia: false
		}),
		// Performance monitoring - without unsupported options
		browserTracingIntegration(),
		// Get more context on http requests
		httpContextIntegration()
	],

	// Process errors before sending to Sentry
	/* beforeSend(event, hint) {
		// Don't send PII data
		if (event.user) {
			// Allow sending user IDs but not identifiable information
			delete event.user.email;
			delete event.user.ip_address;
		}

		// You could add custom logic here to filter or modify events
		return event;
	} */
});

// If you have a custom error handler, pass it to `handleErrorWithSentry`
export const handleError = handleErrorWithSentry();
