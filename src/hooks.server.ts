import { sequence } from '@sveltejs/kit/hooks';
import {
	handleErrorWithSentry,
	sentryHandle,
	requestDataIntegration,
	httpIntegration,
	contextLinesIntegration,
	localVariablesIntegration
} from '@sentry/sveltekit';
import * as Sentry from '@sentry/sveltekit';
import type { Handle, ResolveOptions, ServerInit } from '@sveltejs/kit'; 
import { dev } from '$app/environment';
import { getDb } from '$lib/server/db'; 

// Initialize Sentry
Sentry.init({
	dsn: 'https://<EMAIL>/4509055809748992',
	environment: dev ? 'development' : 'production',
	tracesSampleRate: dev ? 1.0 : 0.1,
	debug: dev,
	includeLocalVariables: true,
	initialScope: {
		tags: {
			app: 'arisetransit-backend',
			framework: 'sveltekit'
		}
	},
	ignoreErrors: [/ECONNRESET/, /socket hang up/i, 'OperationCancelled', /413 Payload Too Large/i],
	shutdownTimeout: 5000,
	integrations: [
		requestDataIntegration(),
		httpIntegration(),
		localVariablesIntegration(),
		contextLinesIntegration({
			frameContextLines: 7
		})
	],
	beforeSend(event, _hint) {
		if (event.user) {
			delete event.user.email;
			delete event.user.ip_address;
		}
		return event;
	}
});

// Initialize DB connection once on server startup using the init hook
export const init: ServerInit = async () => {
	try {
		await getDb();
		console.log('Database connection initialized via init hook.'); 
	} catch (error) {
		console.error('Failed to initialize database connection via init hook:', error);
		// Decide how to handle startup failure - maybe throw to stop server?
		// For now, we log the error. Depending on the app's needs,
		// you might want to throw the error to prevent the server from starting
		// if the DB connection is absolutely critical.
		// throw new Error('Database initialization failed');
	}
};

// Custom handler to capture 404 errors
const handle404s: Handle = async ({ event, resolve }) => {
	const response = await resolve(event);

	if (response.status === 404) {
		Sentry.captureMessage(`404 Not Found: ${event.url.pathname}`, {
			level: 'warning',
			tags: {
				path: event.url.pathname,
				status: 404,
				referrer: event.request.headers.get('referer') || 'unknown'
			}
		});
	}

	// Also track server errors for logging
	if (response.status >= 500) {
		Sentry.captureMessage(`Server Error: ${response.status} at ${event.url.pathname}`, {
			level: 'error',
			tags: {
				path: event.url.pathname,
				status: response.status
			}
		});
	}

	return response;
};

// NEW: Handle function for font preloading
const handleFontPreload: Handle = async ({ event, resolve }) => {
	// Define the preload filter
	const preloadFilter = (input: { type: "font" | "css" | "js" | "asset"; path: string }): boolean => {
		// Preload all woff2 font files
		// This covers both Playfair Display Variable and Poppins from Fontsource
		// and is robust if you add more fonts later.
		return input.type === 'font' && input.path.endsWith('.woff2');
	};

	// Resolve the request, adding the preload filter
	// We pass `ssr: true` (default) and `transformPageChunk` (default)
	// Explicitly pass preload here
	const resolveOptions: ResolveOptions = {
		preload: preloadFilter
		// transformPageChunk and ssr will use their defaults if not specified
	};

	return resolve(event, resolveOptions);
};

// Sequence the handlers: Font Preload -> Sentry -> 404s
// Add handleFontPreload to the sequence
export const handle = sequence(handleFontPreload, sentryHandle(), handle404s);

// If you have a custom error handler, pass it to `handleErrorWithSentry`
export const handleError = handleErrorWithSentry();
