Help me fix my og image vercel implemenetation. @/home/<USER>/dev/arisetransit/src/routes/og/+server.ts 

Docs

pen Graph (OG) Image Generation
To assist with generating dynamic Open Graph (OG) images, you can use the Vercel @vercel/og library to compute and generate social card images using Vercel Functions.

Benefits
Performance: With a small amount of code needed to generate images, functions can be started almost instantly. This allows the image generation process to be fast and recognized by tools like the Open Graph Debugger
Ease of use: You can define your images using HTML and CSS and the library will dynamically generate images from the markup
Cost-effectiveness: @vercel/og automatically adds the correct headers to cache computed images at the edge, helping reduce cost and recomputation
Supported features
Basic CSS layouts including flexbox and absolute positioning
Custom fonts, text wrapping, centering, and nested images
Ability to download the subset characters of the font from Google Fonts
Compatible with any framework and application deployed on Vercel
View your OG image and other metadata before your deployment goes to production through the Open Graph tab
Runtime support
Vercel OG image generation is supported on the Node.js runtime.

Local resources can be loaded directly using fs.readFile. Alternatively, fetch can be used to load remote resources.

og.js

const fs = require('fs').promises;
 
const loadLocalImage = async () => {
  const imageData = await fs.readFile('/path/to/image.png');
  // Process image data
};
Runtime caveats
There are limitations when using vercel/og with the Next.js Pages Router and the Node.js runtime. Specifically, this combination does not support the return new Response(…) syntax. The table below provides a breakdown of the supported syntaxes for different configurations.

Configuration	Supported Syntax	Notes
pages/ + Edge runtime	return new Response(…)	Fully supported.
app/ + Node.js runtime	return new Response(…)	Fully supported.
app/ + Edge runtime	return new Response(…)	Fully supported.
pages/ + Node.js runtime	Not supported	Does not support return new Response(…) syntax with vercel/og.
Usage
Requirements
Install Node.js 22 or newer by visiting nodejs.org
Install @vercel/og by running the following command inside your project directory. This isn't required for Next.js App Router projects, as the package is already included:
pnpm
yarn
npm
bun

pnpm i @vercel/og
For Next.js implementations, make sure you are using Next.js v12.2.3 or newer
Create API endpoints that you can call from your front-end to generate the images. Since the HTML code for generating the image is included as one of the parameters of the ImageResponse function, the use of .jsx or .tsx files is recommended as they are designed to handle this kind of syntax
To avoid the possibility of social media providers not being able to fetch your image, it is recommended to add your OG image API route(s) to Allow inside your robots.txt file. For example, if your OG image API route is /api/og/, you can add the following line:
robots.txt

Allow: /api/og/*
If you are using Next.js, review robots.txt to learn how to add or generate a robots.txt file.
Getting started
Get started with an example that generates an image from static text using Next.js by setting up a new app with the following command:

pnpm
yarn
npm
bun

pnpm create next-app
Create an API endpoint by adding og.tsx under the api directory in the root of your project.

Then paste the following code:

Next.js (/app)
Next.js (/pages)
Other frameworks
api/og.tsx
TypeScript

TypeScript

import { ImageResponse } from '@vercel/og';
 
export default async function handler() {
  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 40,
          color: 'black',
          background: 'white',
          width: '100%',
          height: '100%',
          padding: '50px 200px',
          textAlign: 'center',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        👋 Hello
      </div>
    ),
    {
      width: 1200,
      height: 630,
    },
  );
}
If you're not using a framework, you must either add "type": "module" to your package.json or change your JavaScript Functions' file extensions from .js to .mjs

Run the following command:

pnpm
yarn
npm
bun

pnpm dev
Then, browse to http://localhost:3000/api/og. You will see the following image:


Consume the OG route
Deploy your project to obtain a publicly accessible path to the OG image API endpoint. You can find an example deployment at https://og-examples.vercel.sh/api/static.

Then, based on the Open Graph Protocol, create the web content for your social media post as follows:

Create a <meta> tag inside the <head> of the webpage
Add the property attribute with value og:image to the <meta> tag
Add the content attribute with value as the absolute path of the /api/og endpoint to the <meta> tag
With the example deployment at https://og-examples.vercel.sh/api/static, use the following code:

index.js

<head>
  <title>Hello world</title>
  <meta
    property="og:image"
    content="https://og-examples.vercel.sh/api/static"
  />
</head>
Every time you create a new social media post, you need to update the API endpoint with the new content. However, if you identify which parts of your ImageResponse will change for each post, you can then pass those values as parameters of the endpoint so that you can use the same endpoint for all your posts.

In the examples below, we explore using parameters and including other types of content with ImageResponse.

Examples
Dynamic title: Passing the image title as a URL parameter
Dynamic external image: Passing the username as a URL parameter to pull an external profile image for the image generation
Emoji: Using emojis to generate the image
SVG: Using SVG embedded content to generate the image
Custom font: Using a custom font available in the file system to style your image title
Tailwind CSS: Using Tailwind CSS (Experimental) to style your image content
Internationalization: Using other languages in the text for generating your image
Secure URL: Encrypting parameters so that only certain values can be passed to generate your image
Technical details
Recommended OG image size: 1200x630 pixels
@vercel/og uses Satori and Resvg to convert HTML and CSS into PNG
@vercel/og API reference
Limitations
Only ttf, otf, and woff font formats are supported. To maximize the font parsing speed, ttf or otf are preferred over woff
Only flexbox (display: flex) and a subset of CSS properties are supported. Advanced layouts (display: grid) will not work. See Satori's documentation for more details on supported CSS properties
Maximum bundle size of 500KB. The bundle size includes your JSX, CSS, fonts, images, and any other assets. If you exceed the limit, consider reducing the size of any assets or fetching at runtime
Last updated on April 28, 2025

vercel uses satori under the hood. 

Satori docs talk about not needing to use jsx too 

https://github.com/vercel/satori

Satori: Enlightened library to convert HTML and CSS to SVG.

Note

To use Satori in your project to generate PNG images like Open Graph images and social cards, check out our announcement and Vercel’s Open Graph Image Generation →

To use it in Next.js, take a look at the Next.js Open Graph Image Generation template →

Overview
Satori supports the JSX syntax, which makes it very straightforward to use. Here’s an overview of the basic usage:

// api.jsx
import satori from 'satori'

const svg = await satori(
  <div style={{ color: 'black' }}>hello, world</div>,
  {
    width: 600,
    height: 400,
    fonts: [
      {
        name: 'Roboto',
        // Use `fs` (Node.js only) or `fetch` to read the font as Buffer/ArrayBuffer and provide `data` here.
        data: robotoArrayBuffer,
        weight: 400,
        style: 'normal',
      },
    ],
  },
)
Satori will render the element into a 600×400 SVG, and return the SVG string:

'<svg ...><path d="..." fill="black"></path></svg>'
Under the hood, it handles layout calculation, font, typography and more, to generate a SVG that matches the exact same HTML and CSS in a browser.


Documentation
JSX
Satori only accepts JSX elements that are pure and stateless. You can use a subset of HTML elements (see section below), or custom React components, but React APIs such as useState, useEffect, dangerouslySetInnerHTML are not supported.

Use without JSX
If you don't have JSX transpiler enabled, you can simply pass React-elements-like objects that have type, props.children and props.style (and other properties too) directly:

await satori(
  {
    type: 'div',
    props: {
      children: 'hello, world',
      style: { color: 'black' },
    },
  },
  options
)
HTML Elements
Satori supports a limited subset of HTML and CSS features, due to its special use cases. In general, only these static and visible elements and properties that are implemented.

For example, the <input> HTML element, the cursor CSS property are not in consideration. And you can't use <style> tags or external resources via <link> or <script>.

Also, Satori does not guarantee that the SVG will 100% match the browser-rendered HTML output since Satori implements its own layout engine based on the SVG 1.1 spec.

You can find the list of supported HTML elements and their preset styles here.

Images
You can use <img> to embed images. However, width, and height attributes are recommended to set:

await satori(
  <img src="https://picsum.photos/200/300" width={200} height={300} />,
  options
)
When using background-image, the image will be stretched to fit the element by default if you don't specify the size.

If you want to render the generated SVG to another image format such as PNG, it would be better to use base64 encoded image data (or buffer) directly as props.src so no extra I/O is needed in Satori:

await satori(
  <img src="data:image/png;base64,..." width={200} height={300} />,
  // Or src={arrayBuffer}, src={buffer}
  options
)
CSS
Satori uses the same Flexbox layout engine as React Native, and it’s not a complete CSS implementation. However, it supports a subset of the spec that covers most common CSS features:

Property	Property Expanded	Supported Values	Example
display	none and flex, default to flex	
position	relative and absolute, default to relative	
color	Supported	
margin
marginTop	Supported	
marginRight	Supported	
marginBottom	Supported	
marginLeft	Supported	
Position
top	Supported	
right	Supported	
bottom	Supported	
left	Supported	
Size
width	Supported	
height	Supported	
Min & max size
minWidth	Supported except for min-content, max-content and fit-content	
minHeight	Supported except for min-content, max-content and fit-content	
maxWidth	Supported except for min-content, max-content and fit-content	
maxHeight	Supported except for min-content, max-content and fit-content	
border
Width (borderWidth, borderTopWidth, ...)	Supported	
Style (borderStyle, borderTopStyle, ...)	solid and dashed, default to solid	
Color (borderColor, borderTopColor, ...)	Supported	
Shorthand (border, borderTop, ...)	Supported, i.e. 1px solid gray
borderRadius
borderTopLeftRadius	Supported	
borderTopRightRadius	Supported	
borderBottomLeftRadius	Supported	
borderBottomRightRadius	Supported	
Shorthand	Supported, i.e. 5px, 50% / 5px	
Flex
flexDirection	column, row, row-reverse, column-reverse, default to row	
flexWrap	wrap, nowrap, wrap-reverse, default to wrap	
flexGrow	Supported	
flexShrink	Supported	
flexBasis	Supported except for auto	
alignItems	stretch, center, flex-start, flex-end, baseline, normal, default to stretch	
alignContent	Supported	
alignSelf	Supported	
justifyContent	Supported	
gap	Supported	
Font
fontFamily	Supported	
fontSize	Supported	
fontWeight	Supported	
fontStyle	Supported	
Text
tabSize	Supported	
textAlign	start, end, left, right, center, justify, default to start	
textTransform	none, lowercase, uppercase, capitalize, defaults to none	
textOverflow	clip, ellipsis, defaults to clip	
textDecoration	Support line types underline and line-through, and styles dotted, dashed, solid	Example
textShadow	Supported	
lineHeight	Supported	
letterSpacing	Supported	
whiteSpace	normal, pre, pre-wrap, pre-line, nowrap, defaults to normal	
wordBreak	normal, break-all, break-word, keep-all, defaults to normal	
textWrap	wrap, balance, defaults to wrap	
Background
backgroundColor	Supported, single value	
backgroundImage	linear-gradient, radial-gradient, url, single value	
backgroundPosition	Support single value	
backgroundSize	Support two-value size i.e. 10px 20%	
backgroundClip	border-box, text	
backgroundRepeat	repeat, repeat-x, repeat-y, no-repeat, defaults to repeat	
transform
Translate (translate, translateX, translateY)	Supported	
Rotate	Supported	
Scale (scale, scaleX, scaleY)	Supported	
Skew (skew, skewX, skewY)	Supported	
transformOrigin	Support one-value and two-value syntax (both relative and absolute values)	
objectFit	contain, cover, none, default to none	
opacity	Supported	
boxShadow	Supported	
overflow	visible and hidden, default to visible	
filter	Supported	
clipPath	Supported	Example
lineClamp	Supported	Example
Mask
maskImage	linear-gradient(...), radial-gradient(...), url(...)	Example
maskPosition	Supported	Example
maskSize	Support two-value size i.e. 10px 20%	Example
maskRepeat	repeat, repeat-x, repeat-y, no-repeat, defaults to repeat	Example
WebkitTextStroke	WebkitTextStrokeWidth	Supported	
WebkitTextStrokeColor	Supported	
Note:

Three-dimensional transforms are not supported.
There is no z-index support in SVG. Elements that come later in the document will be painted on top.
box-sizing is set to border-box for all elements.
calc isn't supported.
currentcolor support is only available for the color property.
Language and Typography
Advanced typography features such as kerning, ligatures and other OpenType features are not currently supported.

RTL languages are not supported either.

Fonts
Satori currently supports three font formats: TTF, OTF and WOFF. Note that WOFF2 is not supported at the moment. You must specify the font if any text is rendered with Satori, and pass the font data as ArrayBuffer (web) or Buffer (Node.js):

await satori(
  <div style={{ fontFamily: 'Inter' }}>Hello</div>,
  {
    width: 600,
    height: 400,
    fonts: [
      {
        name: 'Inter',
        data: inter,
        weight: 400,
        style: 'normal',
      },
      {
        name: 'Inter',
        data: interBold,
        weight: 700,
        style: 'normal',
      },
    ],
  }
)
Multiple fonts can be passed to Satori and used in fontFamily.

Tip

We recommend you define global fonts instead of creating a new object and pass it to satori for better performance, if your fonts do not change. Read it for more detail

Emojis
To render custom images for specific graphemes, you can use graphemeImages option to map the grapheme to an image source:

await satori(
  <div>Next.js is 🤯!</div>,
  {
    ...,
    graphemeImages: {
      '🤯': 'https://cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/svg/1f92f.svg',
    },
  }
)
The image will be resized to the current font-size (both width and height) as a square.

Locales
Satori supports rendering text in different locales. You can specify the supported locales via the lang attribute:

await satori(
  <div lang="ja-JP">骨</div>
)
Same characters can be rendered differently in different locales, you can specify the locale when necessary to force it to render with a specific font and locale. Check out this example to learn more.

Supported locales are exported as the Locale enum type.

Dynamically Load Emojis and Fonts
Satori supports dynamically loading emoji images (grapheme pictures) and fonts. The loadAdditionalAsset function will be called when a text segment is rendered but missing the image or font:

await satori(
  <div>👋 你好</div>,
  {
    // `code` will be the detected language code, `emoji` if it's an Emoji, or `unknown` if not able to tell.
    // `segment` will be the content to render.
    loadAdditionalAsset: async (code: string, segment: string) => {
      if (code === 'emoji') {
        // if segment is an emoji
        return `data:image/svg+xml;base64,...`
      }

      // if segment is normal text
      return loadFontFromSystem(code)
    }
  }
)
Runtime and WASM
Satori can be used in browser, Node.js (>= 16), and Web Workers.

By default, Satori depends on asm.js for the browser runtime, and native module in Node.js. However, you can optionally load WASM instead by importing satori/wasm and provide the initialized WASM module instance of Yoga to Satori:

import satori, { init } from 'satori/wasm'
import initYoga from 'yoga-wasm-web'

const yoga = initYoga(await fetch('/yoga.wasm').then(res => res.arrayBuffer()))
init(yoga)

await satori(...)
When running in the browser or in the Node.js environment, WASM files need to be hosted and fetched before initializing. asm.js can be bundled together with the lib. In this case WASM should be faster.

When running on the Node.js server, native modules should be faster. However there are Node.js environments where native modules are not supported (e.g. StackBlitz's WebContainers), or other JS runtimes that support WASM (e.g. Vercel's Edge Runtime, Cloudflare Workers, or Deno).

Additionally, there are other difference between asm.js, native and WASM, such as security and compatibility.

Overall there are many trade-offs between each choice, and it's better to pick the one that works the best for your use case.

Font Embedding
By default, Satori renders the text as <path> in SVG, instead of <text>. That means it embeds the font path data as inlined information, so succeeding processes (e.g. render the SVG on another platform) don’t need to deal with font files anymore.

You can turn off this behavior by setting embedFont to false, and Satori will use <text> instead:

const svg = await satori(
  <div style={{ color: 'black' }}>hello, world</div>,
  {
    ...,
    embedFont: false,
  },
)
Pixel Grid Rounding
Set pointScaleFactor to control how layout values are rounded to the pixel grid. This parameter is passed directly to Yoga’s pointScaleFactor and improves rendering precision on high-DPI displays.

const svg = await satori(
  <div style={{ color: 'black' }}>hello, world</div>,
  {
    ...,
    pointScaleFactor: 2,
  },
)
Debug
To draw the bounding box for debugging, you can pass debug: true as an option:

const svg = await satori(
  <div style={{ color: 'black' }}>hello, world</div>,
  {
    ...,
    debug: true,
  },
)

Contribute

# @vercel/og Reference

The package exposes an `ImageResponse` constructor, with the following parameters:

```
import { ImageResponse } from '@vercel/og'
 
new ImageResponse(
  element: ReactElement,
  options: {
    width?: number = 1200
    height?: number = 630
    emoji?: 'twemoji' | 'blobmoji' | 'noto' | 'openmoji' = 'twemoji',
    fonts?: {
      name: string,
      data: ArrayBuffer,
      weight: number,
      style: 'normal' | 'italic'
    }[]
    debug?: boolean = false
 
    // Options that will be passed to the HTTP response
    status?: number = 200
    statusText?: string
    headers?: Record<string, string>
  },
)
```

Open inOpen in v0

### [Main parameters](#main-parameters)

| Parameter | Type | Default | Description |
| --- | --- | --- | --- |
| `element` | `ReactElement` | — | The React element to generate the image from. |
| `options` | `object` | — | Options to customize the image and HTTP response. |

### [Options parameters](#options-parameters)

| Parameter | Type | Default | Description |
| --- | --- | --- | --- |
| `width` | `number` | `1200` | The width of the image. |
| `height` | `number` | `630` | The height of the image. |
| `emoji` | `twemoji` `blobmoji` `noto` `openmoji` `twemoji` | The emoji set to use. |  |
| `debug` | `boolean` | `false` | Debug mode flag. |
| `status` | `number` | `200` | The HTTP status code for the response. |
| `statusText` | `string` | — | The HTTP status text for the response. |
| `headers` | `Record<string, string>` | — | The HTTP headers for the response. |

### [Fonts parameters (within options)](#fonts-parameters-within-options)

| Parameter | Type | Default | Description |
| --- | --- | --- | --- |
| `name` | `string` | — | The name of the font. |
| `data` | `ArrayBuffer` | — | The font data. |
| `weight` | `number` | — | The weight of the font. |
| `style` | `normal` `italic` | — | The style of the font. |

By default, the following headers will be included by `@vercel/og`:

```
'content-type': 'image/png',
'cache-control': 'public, immutable, no-transform, max-age=31536000',
```

## [Supported HTML and CSS features](#supported-html-and-css-features)

Refer to [Satori's documentation](https://github.com/vercel/satori#documentation) for a list of supported HTML and CSS features.

By default, `@vercel/og` only has the Noto Sans font included. If you need to use other fonts, you can pass them in the `fonts` option. View the [custom font example](/docs/recipes/using-custom-font) for more details.

## [Acknowledgements](#acknowledgements)

*   [Twemoji](https://github.com/twitter/twemoji)
*   [Google Fonts](https://fonts.google.com) and [Noto Sans](https://www.google.com/get/noto/)
*   [Resvg](https://github.com/RazrFalcon/resvg) and [Resvg.js](https://github.com/yisibl/resvg-js)

Last updated on March 4, 2025