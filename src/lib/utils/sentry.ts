import * as Sentry from '@sentry/sveltekit';
import type { SeverityLevel } from '@sentry/sveltekit';

/**
 * Utility functions for Sentry error tracking
 */

/**
 * Identify the current user for error tracking
 * Only call this when you have the user's consent
 *
 * @param userId User's unique identifier
 * @param username Optional username (avoid using email)
 * @param metadata Optional additional user info
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function identifyUser(userId: string, username?: string, metadata?: Record<string, any>) {
	Sentry.setUser({
		id: userId,
		username,
		...metadata
	});
}

/**
 * Clear user identification data
 * Call this when the user logs out
 */
export function clearUserIdentity() {
	Sentry.setUser(null);
}

/**
 * Add custom context to the current scope
 *
 * @param name Context name
 * @param data Context data
 */
export function addContext(name: string, data: Record<string, unknown>) {
	Sentry.setContext(name, data);
}

/**
 * Add a breadcrumb to track user actions
 *
 * @param message Breadcrumb message
 * @param category Category (e.g., "ui.click", "navigation")
 * @param level Severity level
 * @param data Additional data
 */
export function addBreadcrumb(
	message: string,
	category?: string,
	level: SeverityLevel = 'info',
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	data?: Record<string, any>
) {
	Sentry.addBreadcrumb({
		message,
		category: category || 'app',
		level,
		data
	});
}

/**
 * Manually capture an error
 *
 * @param error Error object or message
 * @param context Additional context
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function captureError(error: Error | string, context?: Record<string, any>) {
	if (typeof error === 'string') {
		Sentry.captureMessage(error, {
			level: 'error',
			extra: context
		});
	} else {
		Sentry.captureException(error, {
			extra: context
		});
	}
}

/**
 * Set global tags that will be sent with every event
 *
 * @param tags Tags object
 */
export function setTags(tags: Record<string, string>) {
	Object.entries(tags).forEach(([key, value]) => {
		Sentry.setTag(key, value);
	});
}

/**
 * Track a form submission event
 *
 * @param formName Form identifier
 * @param success Whether submission was successful
 * @param data Optional form data (exclude sensitive info)
 */
export function trackFormSubmission(
	formName: string,
	success: boolean,
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	data?: Record<string, any>
) {
	addBreadcrumb(
		`Form ${formName} submitted - ${success ? 'success' : 'failed'}`,
		'form.submit',
		success ? 'info' : 'error',
		data
	);
}
