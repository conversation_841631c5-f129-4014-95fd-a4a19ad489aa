/**
 * Modern viewport utility for Svelte 5
 *
 * Provides reactive viewport information using Svelte stores.
 * Features:
 * - Viewport dimensions (width, height)
 * - Breakpoint detection (sm, md, lg, xl, 2xl) matching Tailwind/daisyUI
 * - Media query matching
 * - SSR-safe with sensible defaults
 * - TypeScript support
 */

import { browser } from '$app/environment';
import { readable, derived } from 'svelte/store';

// Tailwind/daisyUI breakpoints in pixels
export const breakpoints = {
	sm: 640, // Small devices (phones, 640px and up)
	md: 768, // Medium devices (tablets, 768px and up)
	lg: 1024, // Large devices (desktops, 1024px and up)
	xl: 1280, // Extra large devices (large desktops, 1280px and up)
	'2xl': 1536 // 2X-Large devices (larger desktops, 1536px and up)
} as const;

export type Breakpoint = keyof typeof breakpoints;

/**
 * Single store representing viewport dimensions and breakpoint matches.
 */
export const viewport = readable(
	{ 
		width: browser ? window.innerWidth : breakpoints.md - 1,
		height: browser ? window.innerHeight : 800,
		sm: false, 
		md: false, 
		lg: false, 
		xl: false, 
		'2xl': false 
	},
	(set) => {
		if (!browser) return;
		
		function update() {
			const w = window.innerWidth;
			set({
				width: w,
				height: window.innerHeight,
				sm: w >= breakpoints.sm,
				md: w >= breakpoints.md,
				lg: w >= breakpoints.lg,
				xl: w >= breakpoints.xl,
				'2xl': w >= breakpoints['2xl']
			});
		}
		
		// Initial update to set correct values
		update();
		
		// Use passive listener for better performance
		window.addEventListener('resize', update, { passive: true });
		
		// Return cleanup function
		return () => window.removeEventListener('resize', update);
	}
);

// Derived stores for dimensions
export const width = derived(viewport, $v => $v.width);
export const height = derived(viewport, $v => $v.height);

// Device category stores
export const isMobile = derived(viewport, $v => $v.width < breakpoints.md);
export const isTablet = derived(viewport, $v => $v.width >= breakpoints.md && $v.width < breakpoints.lg);
export const isDesktop = derived(viewport, $v => $v.width >= breakpoints.lg);

// Active breakpoint as a derived store
export const activeBreakpoint = derived(viewport, $v => {
	const w = $v.width;
	if (w >= breakpoints['2xl']) return '2xl';
	if (w >= breakpoints.xl) return 'xl';
	if (w >= breakpoints.lg) return 'lg';
	if (w >= breakpoints.md) return 'md';
	if (w >= breakpoints.sm) return 'sm';
	return 'xs';
});

// Helper factory functions returning derived stores
export function atLeast(bp: Breakpoint) {
	return derived(viewport, $v => $v[bp]);
}

export function below(bp: Breakpoint) {
	return derived(viewport, $v => !$v[bp]);
}

export function between(min: Breakpoint, max: Breakpoint) {
	return derived(viewport, $v => {
		const minMatch = $v[min];
		const nextKey = Object.keys(breakpoints)[Object.keys(breakpoints).indexOf(max) + 1] as Breakpoint | undefined;
		const maxMatch = max === '2xl' || !nextKey ? true : !$v[nextKey];
		return minMatch && maxMatch;
	});
}

// Media query matching store
export function matchesMediaQuery(query: string) {
	return readable(
		browser ? window.matchMedia(query).matches : false,
		(set) => {
			if (!browser) return;
			
			const mql = window.matchMedia(query);
			const handler = () => set(mql.matches);
			
			mql.addEventListener('change', handler);
			return () => mql.removeEventListener('change', handler);
		}
	);
}

// For backward compatibility with context pattern
export const viewportContext = viewport;