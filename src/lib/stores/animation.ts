import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface AnimationState {
	time: number;
	scrollProgress: number;
	enabled: boolean;
}

function createAnimationStore() {
	const { subscribe, set, update } = writable<AnimationState>({
		time: 0,
		scrollProgress: 0,
		enabled: false
	});

	let animationFrameId: number | null = null;
	let startTime = 0;
	let isRunning = false;
	let currentEnabled = false; // Track internal enabled state

	function startLoop() {
		if (!browser || isRunning) return;
		console.log('Animation Store: Starting loop');
		isRunning = true;
		startTime = performance.now();
		tick();
	}

	function stopLoop() {
		if (!browser || !isRunning) return;
		console.log('Animation Store: Stopping loop');
		if (animationFrameId !== null) {
			cancelAnimationFrame(animationFrameId);
			animationFrameId = null;
		}
		isRunning = false;
	}

	function tick() {
		if (!isRunning) return;
		const currentTime = (performance.now() - startTime) / 1000.0;
		update((state) => ({ ...state, time: currentTime }));
		animationFrameId = requestAnimationFrame(tick);
	}

	// Automatically start/stop loop based on enabled state changes
	subscribe((state) => {
		if (state.enabled && !currentEnabled) {
			currentEnabled = true;
			startLoop();
		} else if (!state.enabled && currentEnabled) {
			currentEnabled = false;
			stopLoop();
		}
	});

	// Cleanup on module unload (though less critical for stores)
	// if (browser && typeof window !== 'undefined') {
	// 	window.addEventListener('beforeunload', stopLoop);
	// }

	return {
		subscribe,
		setEnabled: (enabled: boolean) => update((state) => ({ ...state, enabled })),
		setScrollProgress: (progress: number) => update((state) => ({ ...state, scrollProgress: progress })),
		// Expose set for potential direct state setting if needed, though less common
		set
	};
}

export const animationStore = createAnimationStore();