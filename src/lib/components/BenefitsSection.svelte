<script lang="ts">
	import { CheckCircle, Users, Clock } from 'lucide-svelte';

	let {
		title = 'Why Choose Arise Transit?',
		benefits = [
			{
				icon: 'check',
				title: 'Premium Vehicles',
				description: 'Newest luxury models with full amenities'
			},
			{
				icon: 'users',
				title: 'Professional Drivers',
				description: 'Licensed, experienced, and background-checked'
			},
			{
				icon: 'clock',
				title: '24/7 Availability',
				description: 'Always ready when you need us'
			}
		]
	} = $props<{
		title?: string;
		benefits?: Array<{
			icon: 'check' | 'users' | 'clock';
			title: string;
			description: string;
		}>;
	}>();
</script>

<div class="flex flex-col bg-neutral p-4 md:p-6 lg:p-8 gap-2 md:gap-4 lg:gap-6">
	<div class="py-12 text-center">
		<h2 class="mb-4 text-2xl font-bold">{title}</h2>
		<div class="mt-8 grid grid-cols-1 gap-6 px-4 md:grid-cols-3">
			{#each benefits as benefit}
				<article class="card bg-base-100 shadow-lg transition-all hover:shadow-xl">
					<div class="card-body items-center text-center">
						<div class="text-primary mb-2" aria-hidden="true">
							{#if benefit.icon === 'check'}
								<CheckCircle size={24} strokeWidth={1.5} />
							{:else if benefit.icon === 'users'}
								<Users size={24} strokeWidth={1.5} />
							{:else if benefit.icon === 'clock'}
								<Clock size={24} strokeWidth={1.5} />
							{/if}
						</div>
						<h3 class="card-title mt-2">{benefit.title}</h3>
						<p class="text-sm">{benefit.description}</p>
					</div>
				</article>
			{/each}
		</div>
	</div>
</div>
