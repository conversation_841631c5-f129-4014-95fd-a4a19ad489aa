<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { animationStore } from '$lib/stores/animation';
	import { browser } from '$app/environment';
	// Static imports removed for dynamic loading

	// Define types for dynamically imported modules
	type ThreeJsType = typeof import('three');
	type SkyType = typeof import('three/examples/jsm/objects/Sky.js').Sky;

	// Component state
	let containerRef: HTMLDivElement;
	let isLoading = $state(true); // Loading state
	let THREE_Module: ThreeJsType | null = $state(null); // Store imported THREE module
	let renderer: InstanceType<ThreeJsType['WebGLRenderer']> | null = $state(null);
	let scene: InstanceType<ThreeJsType['Scene']> | null = $state(null);
	let camera: InstanceType<ThreeJsType['PerspectiveCamera']> | null = $state(null);
	let sky: InstanceType<SkyType> | null = $state(null); // Use InstanceType for Sky as well
	let width = $state(0);
	let height = $state(0);
	let isVisible = $state(true);
	let sunLight: InstanceType<ThreeJsType['DirectionalLight']> | null = $state(null);

	// Define fog colors using THREE_Module once available
	let dawnFogColor: InstanceType<ThreeJsType['Color']> | null = null; // Initialize as null
	let sunriseFogColor: InstanceType<ThreeJsType['Color']> | null = null; // Initialize as null

	// Asynchronous initialization function
	async function initializeEffect() {
		if (!browser || !containerRef) return;

		try {
			// Dynamically import Three.js and Sky
			THREE_Module = await import('three');
			const { Sky } = await import('three/examples/jsm/objects/Sky.js');

			// Now that THREE_Module is loaded, initialize colors
			dawnFogColor = new THREE_Module.Color(0x7B68EE); // Warmer purple (MediumSlateBlue)
			sunriseFogColor = new THREE_Module.Color(0xE65100); // Deeper rich orange

			// Set up renderer with enhanced color reproduction
			renderer = new THREE_Module.WebGLRenderer({ antialias: true });
			renderer.setPixelRatio(window.devicePixelRatio);
			renderer.toneMapping = THREE_Module.ACESFilmicToneMapping;
			renderer.toneMappingExposure = 0.8;
			renderer.outputColorSpace = THREE_Module.SRGBColorSpace;

			// Set canvas dimensions
			width = containerRef.clientWidth;
			height = containerRef.clientHeight;
			renderer.setSize(width, height);
			containerRef.appendChild(renderer.domElement);

			// Set up scene
			scene = new THREE_Module.Scene();

			// Set up camera
			camera = new THREE_Module.PerspectiveCamera(60, width / height, 0.1, 1000);
			// Null check for camera before accessing properties
			if (camera) {
				camera.position.set(0, 0, 2);
				camera.lookAt(0, 0, 0);
			}

			// Set up sky with enhanced colors
			sky = new Sky();
			// Null check for sky before accessing properties
			if (sky) {
				sky.scale.setScalar(450000);
				scene.add(sky); // Add sky only if it's successfully created
			}

			// Use exponential fog for smoother gradient at dawn with subtle mist
			// Null check for dawnFogColor
			if (scene && dawnFogColor) {
				scene.fog = new THREE_Module.FogExp2(dawnFogColor.getHex(), 0.002); // Increased density for mist
			}

			// Add warm directional light to simulate sunrise glow
			sunLight = new THREE_Module.DirectionalLight(0xffe082, 1.5); // Warmer sun color
			if (scene && sunLight) {
				scene.add(sunLight);
			}
			// Add subtle ambient hemisphere light for ambient glow (richer purple)
			const hemiLight = new THREE_Module.HemisphereLight(0x9370DB, 0x080820, 0.6); // MediumPurple sky color
			if (scene) {
				scene.add(hemiLight);
			}

			// Configure sky material for a vibrant gradient background
			// Null check for sky and its material
			const uniforms = sky?.material.uniforms;
			// Set initial sky parameters only if uniforms exist
			if (uniforms) {
				uniforms.turbidity.value = 10; // Start value
				uniforms.rayleigh.value = 0.5; // Start value - Reduced for more purple
				uniforms.mieCoefficient.value = 0.025; // Slightly increased
				uniforms.mieDirectionalG.value = 0.75; // Start value - Reduced for different scattering
			}

			// Initial sky parameters update based on store value (handled by $effect)

			// Set loading to false after everything is initialized
			isLoading = false;

		} catch (error) {
			console.error("Failed to initialize Three.js effect:", error);
			// Optionally handle the error state in the UI
		}
	}

	// Update sun position based on scroll progress
	function updateSunPosition(progress: number) {
		// Ensure THREE_Module and necessary scene elements are loaded
		if (!THREE_Module || !sky || !scene || !scene.fog || !dawnFogColor || !sunriseFogColor) return;

		// clamp progress to [0,1] to avoid overshooting sky parameters
		const t = THREE_Module.MathUtils.clamp(progress, 0, 1);

		// Map scroll progress to sun elevation - restricted range for sunrise/sunset
		const elevationAngle = THREE_Module.MathUtils.lerp(-0.02, 0.06, t);
		const phi = Math.PI / 2 - elevationAngle;
		const theta = Math.PI * 0.75;

		// Update sky sun position uniform
		// Null check for sky and its material
		const uniforms = sky?.material.uniforms;
		if (!uniforms) return; // Exit if uniforms are not available

		uniforms.sunPosition.value.setFromSphericalCoords(1, phi, theta);

		// Update sky uniforms for sunrise phase
		uniforms.turbidity.value = THREE_Module.MathUtils.lerp(10, 22, t);
		uniforms.rayleigh.value = THREE_Module.MathUtils.lerp(0.5, 1.5, t);
		uniforms.mieCoefficient.value = 0.025;
		uniforms.mieDirectionalG.value = THREE_Module.MathUtils.lerp(0.75, 0.95, t);

		// Animate fog color for sunrise - Add null checks
		if (scene.fog && dawnFogColor && sunriseFogColor) {
			scene.fog.color.lerpColors(dawnFogColor, sunriseFogColor, t);
		}

		// Update directional light position to follow sun
		if (sunLight) {
			const sunPos = uniforms.sunPosition.value.clone().multiplyScalar(100);
			// Null check for sunLight position
			if (sunLight.position) {
				sunLight.position.copy(sunPos);
			}
		}
	}

	// Variable to track previous scroll progress for comparison within the effect
	let prevScrollProgress = $state($animationStore.scrollProgress); // Use $state for reactivity if needed elsewhere, or keep as let if only used in effect

	// Subscribe to animation store changes for rendering and sun position updates
	$effect(() => {
		const { time, scrollProgress } = $animationStore;
		// Removed incorrect $state declaration from here

		// Read state variables locally within the effect's scope
		const currentIsVisible = isVisible;
		const currentSky = sky;
		const currentRenderer = renderer;
		const currentScene = scene;
		const currentCamera = camera;

		// Update sun position only when scrollProgress changes and THREE is loaded
		if (scrollProgress !== prevScrollProgress && currentSky && currentIsVisible && THREE_Module) {
			updateSunPosition(scrollProgress);
			prevScrollProgress = scrollProgress; // Update previous value
		}

		// Render on every frame update from the store if visible and THREE is loaded
		if (currentRenderer && currentScene && currentCamera && currentIsVisible) {
			// Use 'time' variable to prevent unused variable warnings if needed later
			// console.log(`Rendering frame at time: ${time}`);
			currentRenderer.render(currentScene, currentCamera);
		}
	});

	// Initialize on mount
	onMount(() => {
		if (!browser) return;

		// Initialize immediately on mount
		initializeEffect();
		// Removed local animate() call, store now controls rendering

		// Handle window resize
		const handleResize = () => {
			// Ensure THREE_Module is loaded before accessing camera/renderer properties
			if (!browser || !containerRef || !renderer || !camera || !THREE_Module) return;

			width = containerRef.clientWidth;
			height = containerRef.clientHeight;

			// Null checks before accessing camera properties
			if (camera) {
				camera.aspect = width / height;
				camera.updateProjectionMatrix();
			}

			// Null check before accessing renderer properties
			if (renderer) {
				renderer.setSize(width, height);
			}
			// Consider debouncing/throttling this if performance issues arise
		};

		window.addEventListener('resize', handleResize);

		// Set up intersection observer
		const observer = new IntersectionObserver(
			([entry]) => {
				isVisible = entry.isIntersecting;
				animationStore.setEnabled(isVisible);

				if (isVisible) {
					// Immediately update sun position based on current scroll progress
					// when the component becomes visible again.
					const currentProgress = $animationStore.scrollProgress;
					// Ensure THREE is loaded before updating position
					if (THREE_Module) {
						updateSunPosition(currentProgress);
					}
					// Local animation loop start/stop logic removed.
					// The global animationStore now controls enabling/disabling.
				}
			},
			{ threshold: 0 }
		);

		if (containerRef) {
			observer.observe(containerRef);
		}

		return () => {
			if (containerRef) {
				observer.unobserve(containerRef);
			}
			observer.disconnect();
			window.removeEventListener('resize', handleResize); // Clean up resize listener
		};
	});

	// Cleanup on destroy
	onDestroy(() => {
		// Removed cancelAnimationFrame for local loop

		// Check if THREE_Module and its properties exist before disposal
		if (sky && sky.material) {
			sky.material.dispose();
		}

		if (scene) {
			scene.clear(); // This internally disposes geometries/materials if needed
		}

		if (renderer) {
			renderer.dispose();
			if (renderer.domElement && renderer.domElement.parentNode) {
				renderer.domElement.parentNode.removeChild(renderer.domElement);
			}
		}
	});
</script>

<div
	bind:this={containerRef}
	class="absolute inset-0 w-full h-full overflow-hidden"
	class:loaded={!isLoading}
>
	<!-- Canvas will be inserted here by Three.js -->
</div>

<style>
	div {
		background-color: #080010; /* Dark background while loading */
	}

	/* Target the dynamically added canvas for fade-in */
	div :global(canvas) {
		opacity: 0;
		transition: opacity 0.5s ease-in-out; /* Adjust timing as needed */
	}

	/* When the container has the 'loaded' class, fade in the canvas */
	div.loaded :global(canvas) {
		opacity: 1;
	}
</style>