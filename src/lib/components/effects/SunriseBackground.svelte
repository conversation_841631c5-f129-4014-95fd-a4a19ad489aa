<script lang="ts">
	import { onMount, onDestroy, tick } from 'svelte';
	import { tweened } from 'svelte/motion';
	import { cubicOut } from 'svelte/easing';
	import { browser } from '$app/environment';
	import { animationStore } from '$lib/stores/animation'; // Import the store

	// Internal state
	let canvasElement: HTMLCanvasElement | null = $state(null);
	let gl: WebGLRenderingContext | null = $state(null);
	let program: WebGLProgram | null = $state(null);
	let positionBuffer: WebGLBuffer | null = $state(null);
	let positionLocation: number = $state(-1);
	let resolutionLocation: WebGLUniformLocation | null = $state(null);
	let timeLocation: WebGLUniformLocation | null = $state(null);
	let scrollProgressLocation: WebGLUniformLocation | null = $state(null);
	let width = $state(0);
	let height = $state(0);
	let isInitialized = $state(false);
	let isVisible = $state(true); // Start as visible by default
	let resizeTimeout: number | null = $state(null);
	let scrollTimeout: number | null = $state(null); // Added for scroll debouncing

	// Debug flag - enable for troubleshooting
	const DEBUG = false;

	// Use Svelte's tweened store for smooth animation values
	const smoothTime = tweened(0, {
		duration: 300,
		easing: cubicOut
	});

	// Single tweened store for scroll progress with optimized settings
	const smoothScrollProgress = tweened(0, {
		duration: 400, // Shorter duration for more responsive feel
		easing: cubicOut
	});

	// Removed incorrect $derived usage

	const vertexShaderSource = `
		attribute vec2 a_position;
		void main() {
			gl_Position = vec4(a_position, 0.0, 1.0);
		}
	`;

	const fragmentShaderSource = `
		precision mediump float;
		uniform vec2 u_resolution;
		uniform float u_time;
		uniform float u_scrollProgress; // Value from 0 to 1

		// Constants for atmospheric scattering with more natural colors
		const vec3 rayleighCoefficient = vec3(0.32, 0.48, 1.15);    // Softer blue/violet ratio
		const vec3 mieCoefficient = vec3(0.20, 0.20, 0.20);         // Reduced haze for cleaner atmosphere
		const float mieDirectionalG = 0.76;                          // Less focused scattering for softer appearance
		
		// Enhanced color palette for natural transitions
		const vec3 sunsetColor = vec3(2.0, 0.42, 0.18);    // Deep orange-red for sunsets
		const vec3 dayColor = vec3(1.4, 1.35, 1.2);        // Warmer daylight
		const vec3 horizonColor = vec3(1.6, 0.55, 0.25);   // Softer horizon glow
		const vec3 duskColor = vec3(0.6, 0.2, 0.4);        // Deep purple for dusk/dawn transitions

		// Simple noise function
		float noise(vec2 p) {
		    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
		}

		// Rayleigh phase function
		float rayleighPhase(float cosTheta) {
		    return (3.0 / (16.0 * 3.14159)) * (1.0 + cosTheta * cosTheta);
		}

		// Mie phase function (Henyey-Greenstein approximation)
		float miePhase(float cosTheta, float g) {
		    float g2 = g * g;
		    return (1.0 / (4.0 * 3.14159)) * ((1.0 - g2) / pow(1.0 + g2 - 2.0 * g * cosTheta, 1.5));
		}

		void main() {
		    vec2 uv = (gl_FragCoord.xy - 0.5 * u_resolution.xy) / u_resolution.y; // Centered UV, scaled by height
		    float aspect = u_resolution.x / u_resolution.y;
		    vec2 uvAspectCorrected = vec2(uv.x * aspect, uv.y); // Keep aspect corrected uv for some calcs if needed

		          // Define sun position with improved non-linear motion
		          // Calculate sun height starting below horizon and moving upward
		          float sunHeight = smoothstep(-0.8, 0.6, mix(-1.2, 0.4, u_scrollProgress));
		          vec3 sunPosition = normalize(vec3(0.0, sunHeight - 0.4, -1.0));

		          // Horizon line parameters
		          float horizonY = -0.2;  // Slightly below center
		          float horizonWidth = 0.02;  // Thickness of horizon line

		    // View direction (simple orthographic assumption for background)
		    vec3 viewDirection = vec3(0.0, 0.0, 1.0); // Looking forward

		    // Angle between view and sun
		    float cosTheta = dot(viewDirection, sunPosition);

		    // Simulate distance through atmosphere - use original uv.y for vertical position
		    float verticalPosForAirMass = uv.y; // Use non-aspect-corrected y
		    // Enhanced air mass calculation for more realistic atmospheric depth
		    float heightScale = 0.25; // Scale factor for atmosphere height
		    float airMass = 1.0 / (abs(verticalPosForAirMass) + heightScale);
		    airMass = pow(airMass, 1.2); // Adjusted power for better thickness variation
		    airMass = clamp(airMass, 1.0, 20.0); // Allow longer path for more dramatic colors

		    // Calculate scattering contribution (uses cosTheta, largely aspect independent)
		    vec3 scatterR = rayleighCoefficient * rayleighPhase(cosTheta);
		    vec3 scatterM = mieCoefficient * miePhase(cosTheta, mieDirectionalG);
		    // --- Further Reduce extinction effect ---
		    vec3 extinction = exp(-(rayleighCoefficient + mieCoefficient) * airMass * 0.12); // Further reduced extinction for sharper contrast
		    vec3 inscatter = (scatterR + scatterM) * (1.0 - extinction);

		          // Improved soft sun core with Gaussian-like falloff
		          float sunDot = clamp(dot(viewDirection, sunPosition), 0.0, 1.0);
		          float heightFactor = smoothstep(-0.4, 0.2, sunPosition.y);
		          
		          // Softer sun glow using smoothstep and exponential falloff
		          float coreIntensity = smoothstep(0.9985, 0.9995, sunDot);
		          float gaussianFalloff = exp(-pow(1.0 - sunDot, 2.0) * 15.0);
		          float innerGlow = smoothstep(0.0, 0.995, gaussianFalloff);
		          
		          // Dynamic sun colors with more natural transitions
		          vec3 innerSunColor = mix(sunsetColor * 1.8, dayColor * 1.5, heightFactor);
		          vec3 outerSunColor = mix(mix(duskColor, sunsetColor, 0.7), dayColor, heightFactor);
		          
		          // Combine soft core and corona
		          float sunCore = coreIntensity * 2.0 + innerGlow * 0.8;
		          float sunGlow = pow(gaussianFalloff, mix(1.2, 1.8, heightFactor));
		          
		          // Natural horizon glow with height-based variation
		          float horizonGlow = smoothstep(horizonY - horizonWidth * 2.0, horizonY + horizonWidth, uv.y);
		          horizonGlow = pow(1.0 - abs(uv.y - horizonY) * 1.8, 2.5);
		          
		          // Enhanced atmospheric blend
		          vec3 sunContribution = mix(outerSunColor * sunGlow, innerSunColor * sunCore, sunCore);
		          vec3 finalColor = inscatter + sunContribution;
		          
		          // Richer horizon colors with height-based blending
		          vec3 horizonTint = mix(duskColor, horizonColor, heightFactor);
		          finalColor += horizonTint * horizonGlow * (1.0 - heightFactor) * 0.4;

		    // --- Enhanced God Rays with Better Aspect Ratio Handling ---
		    // Use properly scaled UV coordinates for consistent shape
		    vec2 sunScreenPos = vec2(0.0, mix(-0.5, 0.2, u_scrollProgress));
		    // Scale x component by aspect ratio for circular sun shape
		    vec2 diff = vec2(uvAspectCorrected.x, uv.y) - sunScreenPos;
		    float distToSun = length(diff * vec2(1.0, 1.0)); // Uniform scaling
		    
		    // Enhanced god rays with increased intensity
		    float angle = atan(diff.y, diff.x);
		    float heightBasedIntensity = mix(2.0, 1.0, smoothstep(-0.4, 0.2, sunPosition.y));
		    
		    // Multiple layered noise for natural ray variation
		    float basePattern = noise(vec2(angle * 8.0, distToSun * 0.2));
		    float detailPattern = noise(vec2(-angle * 12.0, distToSun * 0.35));
		    float angleVariation = noise(vec2(angle * 3.0, 0.5)); // Angle-based variation
		    
		    // Combine patterns with smooth transitions
		    float rayPattern = mix(
		        pow(basePattern, 0.8) * pow(detailPattern, 0.6),
		        pow(angleVariation, 1.2),
		        0.3
		    );
		    
		    // Softer falloff with distance-based modulation
		    float distanceFalloff = exp(-distToSun * mix(1.2, 0.6, smoothstep(-0.4, 0.2, sunPosition.y)));
		    float softFalloff = smoothstep(1.0, 0.0, distToSun * 0.8);
		    
		    // Combine for natural ray intensity
		    float rayIntensity = distanceFalloff * rayPattern * heightBasedIntensity * softFalloff * 0.9;
		    
		          // Dynamic god ray color based on sun height
		          vec3 rayColor = mix(sunsetColor, vec3(1.0, 0.9, 0.7), heightFactor);
		          finalColor += rayColor * rayIntensity;

		          // Atmospheric haze near horizon
		          float hazeIntensity = smoothstep(0.0, 0.5, 1.0 - abs(uv.y - horizonY));
		          finalColor = mix(finalColor, horizonColor, hazeIntensity * 0.3);

		          // Enhanced atmospheric texture
		          float baseNoise = noise(uvAspectCorrected * 1.5 + u_time * 0.02);
		          float detailNoise = noise(uvAspectCorrected * 3.0 - u_time * 0.015);
		          float combinedNoise = mix(baseNoise, detailNoise, 0.3) * 0.08;
		          
		          // Height-based texture variation
		          float textureIntensity = mix(0.12, 0.06, smoothstep(-0.2, 0.4, uv.y));
		          vec3 textureColor = mix(
		              mix(duskColor, sunsetColor, 0.6),
		              mix(horizonColor, dayColor, heightFactor),
		              smoothstep(-0.2, 0.3, uv.y)
		          );
		          
		          // Apply subtle atmospheric texture
		          finalColor += combinedNoise * textureColor * textureIntensity;

		          // Natural tone mapping and color grading
		          finalColor = pow(finalColor, vec3(1.0/2.4)); // Adjusted gamma for more natural look
		          
		          // Softer highlight rolloff
		          finalColor = finalColor / (finalColor + vec3(0.5)); // Gentler compression
		          
		          // Preserve atmospheric depth
		          float atmosphericDepth = smoothstep(0.2, -0.4, uv.y);
		          finalColor = mix(
		              finalColor,
		              pow(finalColor, vec3(0.85, 0.88, 0.92)),
		              atmosphericDepth * 0.3
		          );
		          
		          // Final exposure adjustment
		          finalColor = pow(finalColor, vec3(0.92)); // Subtle contrast enhancement
		          finalColor = clamp(finalColor * 1.2, 0.0, 1.0); // Controlled exposure boost

		    gl_FragColor = vec4(finalColor, 1.0);
		}
	`;

	// Animation frame ID for cleanup
	let animationFrameId: number | null = $state(null);
	let lastTime = $state(0);

	onMount(() => {
		if (!browser) return;

		// Initialize WebGL context
		initWebGL();

		if (gl) { // Only proceed if WebGL initialization was successful
			const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
			const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

			if (!vertexShader || !fragmentShader) {
				console.error("SunriseBackground: Shader creation failed. Cannot create program.");
				// Handle error appropriately - maybe set a state, stop rendering?
			} else {
				const createdProgram = gl.createProgram();
				if (!createdProgram) {
					console.error("SunriseBackground: Failed to create program.");
				} else {
					program = createdProgram; // Assign early for cleanup potential
					gl.attachShader(program, vertexShader);
					gl.attachShader(program, fragmentShader);
					gl.linkProgram(program);

					const success = gl.getProgramParameter(program, gl.LINK_STATUS);
					if (!success) {
						console.error('SunriseBackground: Program linking failed:', gl.getProgramInfoLog(program));
						gl.deleteProgram(program);
						program = null; // Ensure program remains null on failure
					} else {
						gl.useProgram(program); // Use the program now that it's linked

						// --- Locate attributes and uniforms ---
						positionLocation = gl.getAttribLocation(program, 'a_position');
						timeLocation = gl.getUniformLocation(program, 'u_time');
						resolutionLocation = gl.getUniformLocation(program, 'u_resolution');
						scrollProgressLocation = gl.getUniformLocation(program, 'u_scrollProgress');

						// --- Create buffers ---
						positionBuffer = gl.createBuffer();
						gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
						const positions = [-1, -1, 1, -1, -1, 1, -1, 1, 1, -1, 1, 1]; // Fullscreen quad
						gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

						// --- Set initial viewport and clear color ---
						gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
						gl.clearColor(0, 0, 0, 0); // Set initial clear color to transparent

						// Start the animation loop ONLY if program setup is successful
						if (DEBUG) console.log('SunriseBackground: Program linked successfully. Starting animation loop.');
						startAnimationLoop(); 
					}

					// --- Detach and delete shaders (optional but good practice) ---
					// Shaders are no longer needed after linking
					// Note: Check if program is valid before detaching if link failed
					if (program) {
						gl.detachShader(program, vertexShader);
						gl.detachShader(program, fragmentShader);
					}
					gl.deleteShader(vertexShader);
					gl.deleteShader(fragmentShader);
				}
			}
		} else {
			console.error("SunriseBackground: Cannot setup program because WebGL context is missing.");
		}

		// Set up IntersectionObserver to detect when component is visible
		const observer = new IntersectionObserver((entries) => {
			const [entry] = entries;
			isVisible = entry.isIntersecting;

			// Update animation store based on visibility
			animationStore.setEnabled(isVisible);
		}, {
			threshold: 0 // Trigger as soon as any part is visible
		});

		// Start observing the canvas element
		if (canvasElement) {
			observer.observe(canvasElement);
		}

		// Handle window resize events
		const handleResize = () => {
			// Clear any existing timeout
			if (resizeTimeout !== null) {
				window.clearTimeout(resizeTimeout);
			}

			// Set a timeout to avoid too many resize events
			resizeTimeout = window.setTimeout(() => {
				// Force component to be visible
				isVisible = true;
				isInitialized = true;

				// Reinitialize WebGL context
				if (canvasElement) {
					width = canvasElement.clientWidth;
					height = canvasElement.clientHeight;
					canvasElement.width = width;
					canvasElement.height = height; // Corrected typo here

					// Update WebGL viewport
					if (gl) {
						gl.viewport(0, 0, width, height);

						// Update resolution uniform
						if (resolutionLocation) {
							gl.uniform2f(resolutionLocation, width, height);
						}
					}

					// Force an update of the animation
					updateUniforms();
				}
			}, 100); // 100ms delay
		};

		// Only add resize listener, scroll is handled by Hero component
		window.addEventListener('resize', handleResize, { passive: true });

		return () => {
			// Clean up resources
			if (canvasElement) {
				observer.unobserve(canvasElement);
			}
			observer.disconnect();

			// Clean up resize listener
			window.removeEventListener('resize', handleResize);

			// Clear resize timeout
			if (resizeTimeout !== null) {
				window.clearTimeout(resizeTimeout);
			}

			// Stop animation loop
			stopAnimationLoop();
		};
	});

	function initWebGL() {
		if (!canvasElement || !browser) {
			if (DEBUG) console.log('SunriseBackground: Cannot initialize - missing canvas or not in browser');
			return;
		}

		// Set canvas dimensions to match container
		width = canvasElement.clientWidth || window.innerWidth;
		height = canvasElement.clientHeight || window.innerHeight;
		canvasElement.width = width;
		canvasElement.height = height; // Ensure height is properly set

		// Get WebGL context
		gl = canvasElement.getContext('webgl') as WebGLRenderingContext | null;
		if (!gl) {
			console.error('WebGL not supported or unavailable.');
			if (DEBUG) console.log('SunriseBackground: Failed to get WebGL context.');
			return;
		}

		// Mark as initialized
		isInitialized = true;
		if (DEBUG) console.log(`SunriseBackground: Initialized (${width}x${height})`);

		// Initial uniform update
		updateUniforms();
	}

	// Helper function to create a shader
	function createShader(gl: WebGLRenderingContext, type: number, source: string) {
		const shader = gl.createShader(type);
		if (!shader) {
			console.error('Failed to create shader');
			return null;
		}

		gl.shaderSource(shader, source);
		gl.compileShader(shader);

		const success = gl.getShaderParameter(shader, gl.COMPILE_STATUS);
		if (!success) {
			const shaderType = type === gl.VERTEX_SHADER ? 'VERTEX' : 'FRAGMENT';
			const infoLog = gl.getShaderInfoLog(shader);
			console.error(`Failed to compile ${shaderType} shader:`, infoLog);

			// Log the shader source with line numbers for easier debugging
			const sourceLines = source.split('\n');
			const numberedSource = sourceLines.map((line, i) => `${i+1}: ${line}`).join('\n');
			console.error(`${shaderType} shader source:\n${numberedSource}`);

			gl.deleteShader(shader);
			return null;
		}

		return shader;
	}

	// Update uniforms function
	function updateUniforms() {
		if (!gl || !program || !isInitialized) {
			if (DEBUG) console.log('SunriseBackground: Cannot update uniforms - check state:', { gl: !!gl, program: !!program, isInitialized, isVisible }); 
			return;
		}

		// Get uniform locations if not already cached
		if (timeLocation === null) timeLocation = gl.getUniformLocation(program, 'u_time');
		if (resolutionLocation === null) resolutionLocation = gl.getUniformLocation(program, 'u_resolution');
		if (scrollProgressLocation === null) scrollProgressLocation = gl.getUniformLocation(program, 'u_scrollProgress');

		const currentSmoothedTime = $smoothTime;
		const currentSmoothedScroll = $smoothScrollProgress;

		// Basic check for invalid numbers before sending
		if (isNaN(currentSmoothedTime) || isNaN(currentSmoothedScroll) || isNaN(width) || isNaN(height) || width <= 0 || height <= 0) {
		    if (DEBUG) console.error('SunriseBackground: Invalid uniform value detected!', {
		         time: currentSmoothedTime, width, height, scrollProgress: currentSmoothedScroll
		    });
		    return; 
		}

		// Pass uniforms to the shader
		if (timeLocation !== null) gl.uniform1f(timeLocation, currentSmoothedTime);
		if (resolutionLocation !== null) gl.uniform2f(resolutionLocation, width, height);
		if (scrollProgressLocation !== null) gl.uniform1f(scrollProgressLocation, currentSmoothedScroll);
	}

	// Animation loop
	function startAnimationLoop() {
		if (animationFrameId !== null) return;

		const animate = (time: number) => {
			// Convert time to seconds
			const timeInSeconds = time * 0.001;

			// Only update time when visible
			if (isVisible) {
				// Update time at a controlled rate
				const deltaTime = Math.min(0.1, timeInSeconds - lastTime);
				lastTime = timeInSeconds;
			}

			// Render the scene
			render();

			// Continue the loop
			animationFrameId = requestAnimationFrame(animate);
		};

		// Start the loop
		animationFrameId = requestAnimationFrame(animate);
	}

	function stopAnimationLoop() {
		if (animationFrameId !== null) {
			cancelAnimationFrame(animationFrameId);
			animationFrameId = null;
		}
	}

	// Simplified effect to update scroll progress from store
	$effect(() => {
		const currentScroll = $animationStore.scrollProgress;
		smoothScrollProgress.set(currentScroll);

		// Ensure animation loop is running when enabled
		const currentEnabled = $animationStore.enabled;
		if (currentEnabled && isInitialized && !animationFrameId) {
			startAnimationLoop();
		}
	});

	function render() {
		if (DEBUG) console.log('SunriseBackground: Render loop executing...'); 
		if (!gl || !program || !isInitialized) {
			if (DEBUG) console.log('SunriseBackground: Cannot render - missing context', { gl: !!gl, program: !!program, isInitialized, isVisible }); 
			return;
		}

		if (DEBUG) console.log('SunriseBackground: Rendering frame', { width, height, time: $smoothTime, scrollProgress: $smoothScrollProgress });

		// Clear canvas with a transparent color
		gl.clearColor(0, 0, 0, 0); 
		gl.clear(gl.COLOR_BUFFER_BIT);

		// Update uniforms
		updateUniforms();

		// Set up position attribute
		gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
		gl.enableVertexAttribArray(positionLocation);
		gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

		// Draw
		gl.drawArrays(gl.TRIANGLES, 0, 6);

		if (DEBUG) console.log('SunriseBackground: Rendered successfully');
	}
</script>

<div class="absolute inset-0 w-full h-full overflow-hidden">
	<!-- Canvas with explicit styling to ensure visibility -->
	<canvas 
		bind:this={canvasElement} 
		class="block w-full h-full"
		style="background-color: #080010;"
	></canvas>
	
	<!-- Debug overlay -->
	{#if DEBUG}
	<div class="absolute bottom-0 left-0 bg-black/70 p-2 text-xs text-white z-10">
		WebGL: {gl ? 'Active' : 'Inactive'}<br>
		Initialized: {isInitialized}<br>
		Visible: {isVisible}<br>
		Scroll: {$smoothScrollProgress.toFixed(2)}<br>
		Size: {width}x{height}
	</div>
	{/if}
</div>

<style>
	/* Ensure canvas fills the container and is behind content */
	canvas {
		display: block; /* Removes potential extra space below */
		/* z-index: -10; Provided by Tailwind class */
		/* position: absolute; Provided by Tailwind class */
		/* top: 0; left: 0; width: 100%; height: 100%; Provided by Tailwind class */
	}
</style>