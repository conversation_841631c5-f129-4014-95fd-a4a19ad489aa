<div class="rainbow-bg pointer-events-none absolute inset-0 -z-10"></div>
<div class="h-highlight absolute bottom-0 left-0 w-full" style="box-shadow: 0 0 50vh 40vh #fff;"></div>
<div class="v-highlight absolute bottom-0 left-0 h-full" style="box-shadow: 0 0 35vw 25vw #fff;"></div>

<style>
.rainbow-bg {
  background: conic-gradient(
    from 180deg at 50% 50%,
    #e879f9 0deg,
    #60a5fa 60deg,
    #5eead4 120deg,
    #facc15 180deg,
    #fb7185 240deg,
    #a78bfa 300deg,
    #e879f9 360deg
  );
  opacity: 0.45;
  filter: blur(32px) saturate(1.2);
  width: 100%;
  height: 100%;
  animation: rainbow-spin 18s linear infinite;
  transition: opacity 0.3s;
}
@keyframes rainbow-spin {
  from { transform: scale(1.1) rotate(0deg);}
  to   { transform: scale(1.1) rotate(360deg);}
}
@media (prefers-reduced-motion: reduce) {
  .rainbow-bg { animation: none; }
}
</style>
