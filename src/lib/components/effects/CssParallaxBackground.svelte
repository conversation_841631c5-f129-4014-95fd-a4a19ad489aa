<script lang="ts">
	import { animationStore } from '$lib/stores/animation';

	// We get scrollProgress directly from the store within the $derived calculation below

	// TODO: Add optional mouse interaction later if desired
	// let mouseX = $state(0);
	// let mouseY = $state(0);
	// let containerRef: HTMLElement | undefined = $state();

	// function handleMouseMove(event: MouseEvent) {
	// 	if (!containerRef) return;
	// 	const rect = containerRef.getBoundingClientRect();
	// 	mouseX = event.clientX - rect.left - rect.width / 2;
	// 	mouseY = event.clientY - rect.top - rect.height / 2;
	// }

	// Constants for layer depths and movement factors
	const LAYER_DEPTHS = [-20, -15, -10, -5, 0]; // In pixels (negative = further back)
	const SCROLL_TRANSLATE_FACTOR = 30; // Max pixels to translate vertically based on scroll (Tuned down from debug)

	// Reactive styles for layers based on scroll progress
	let layerStyles = $derived(
		LAYER_DEPTHS.map((depth, index) => {
			// Access scrollProgress directly from the store value here
			const translateY = $animationStore.scrollProgress * SCROLL_TRANSLATE_FACTOR * (1 - index / LAYER_DEPTHS.length); // Layers move at different speeds
			const transform = `translateZ(${depth}px) translateY(${translateY}px)`;
			return `transform: ${transform}; transition: transform 0.3s ease-out;`; // Smooth transition (Adjusted from debug)
		})
	);
</script>

<!-- TODO: Add mousemove handler if implementing mouse interaction -->
<!-- <div bind:this={containerRef} on:mousemove={handleMouseMove} class="parallax-container"> -->
<div class="parallax-container">
	{#each LAYER_DEPTHS as depth, i}
		<div
			class="parallax-layer layer-{i + 1}"
			style={layerStyles[i]}
			aria-hidden="true"
		>
			<!-- Layer content - subtle shapes/gradients -->
		</div>
	{/each}
</div>

<style lang="postcss">
	.parallax-container {
		position: absolute;
		inset: 0;
		overflow: hidden;
		perspective: 1000px; /* Adjust perspective value as needed - Weakened */
		perspective-origin: 50% 50%;
		transform-style: preserve-3d; /* Ensure children are positioned in 3D space */
		pointer-events: none; /* Allow interaction with elements behind */
		background: linear-gradient(to bottom, #0a0a1a, #1a1a2a); /* Dark base gradient */
	}

	.parallax-layer {
		position: absolute;
		inset: 0; /* Make layers same size as container */
		background-color: rgba(255, 255, 255, 0.02); /* Very subtle base color */
		backface-visibility: hidden; /* Performance optimization */
		will-change: transform; /* Hint browser about upcoming changes */
	}

	/* Temporary debug styling for layers */
	.layer-1 {
		/* Deepest layer - Blue */
		background-color: rgba(100, 100, 255, 0.2);
	}
	.layer-2 {
		/* Green */
		background-color: rgba(100, 255, 100, 0.2);
	}
	.layer-3 {
		/* Red */
		background-color: rgba(255, 100, 100, 0.2);
	}
	.layer-4 {
		/* Yellow */
		background-color: rgba(255, 255, 100, 0.2);
	}
	.layer-5 {
		/* Magenta */
		background-color: rgba(255, 100, 255, 0.2);
	}

	/* Reduced motion */
	@media (prefers-reduced-motion: reduce) {
		.parallax-layer {
			transition: none;
			transform: translateZ(var(--depth, 0px)) !important; /* Use CSS var or fallback */
		}
		/* Keep static styles but disable transforms */
		.layer-1 { transform: translateZ(-20px); }
		.layer-2 { transform: translateZ(-15px); }
		.layer-3 { transform: translateZ(-10px); }
		.layer-4 { transform: translateZ(-5px); }
		.layer-5 { transform: translateZ(0px); }
	}
</style>