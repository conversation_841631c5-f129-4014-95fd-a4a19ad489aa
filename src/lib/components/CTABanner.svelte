<script lang="ts">
	import { applyAction } from '$app/forms';
	import { goto } from '$app/navigation';
	import type { Snippet } from 'svelte';
	// Use Tailwind responsive classes instead of viewport utilities
	import { fade, fly } from 'svelte/transition';
	// Import Lucide Svelte icons
	import { ArrowRight } from 'lucide-svelte';

	interface CTAContent {
		mainMessage: string;
		subMessage?: string;
		buttonText?: string;
		buttonHref?: string;
	}

	interface CTABannerProps {
		variant?: 'minimal' | 'image' | 'form';
		content: CTAContent;
		background?: {
			style?: string;
			overlay?: Snippet;
		};
		form?: Snippet<[{ email: string; handleSubmit: () => any }]>;
		class?: string; // Allow passing additional classes
	}

	let { variant = 'minimal', content, background, form, class: bannerClass }: CTABannerProps =
		$props();

	let email = $state('');

	const handleSubmit = () => {
		return async ({ result, update }: { result: any; update: any }) => {
			if (result.type === 'success') {
				// Handle success
				alert('Form submitted successfully!');
			}
			await applyAction(result);
			update();
		};
	};

	const handleError = (error: unknown, reset: () => void) => {
		console.error('Error in CTABanner:', error);
		// Optionally show an error message to the user
		reset();
	};

	function navigateTo(path: string) {
		goto(path);
	}

	// Removed class variables - Apply classes directly in the template
</script>

{#snippet displayContent(data: CTAContent)}
	<div class="py-8 px-4 md:py-12 md:px-6 lg:py-16 lg:px-8">
		<h2 class="font-bold text-2xl mb-2 md:text-3xl md:mb-3 lg:text-4xl lg:mb-4">{data.mainMessage}</h2>
		{#if data.subMessage}
			<p class="text-base mb-4 md:text-lg md:mb-5 lg:text-xl lg:mb-6">{data.subMessage}</p>
		{/if}
		{#if data.buttonText && data.buttonHref}
			<div class="mt-4 flex justify-center md:mt-6 lg:mt-8">
				<a
					href={data.buttonHref}
					aria-label={data.buttonText}
					class="btn btn-primary group hover:scale-105 hover:brightness-110 active:scale-95 transition-all duration-300 shadow-md hover:shadow-lg btn-md md:btn-lg lg:btn-wide no-underline"
				>
					<span class="flex items-center gap-2">
						{data.buttonText}
						<ArrowRight
							class="inline-block h-5 w-5 transition-transform group-hover:translate-x-1"
						/>
					</span>
				</a>
			</div>
		{/if}
	</div>
{/snippet}

<svelte:boundary onerror={handleError}>
	{#if variant === 'minimal'}
		<div class="hero bg-neutral text-neutral-content {bannerClass ?? ''}">
			<div class="hero-content my-4 w-full max-w-5xl text-center md:my-6 lg:my-8">
				<div class="w-full md:max-w-2xl lg:max-w-3xl">
					{@render displayContent(content)}
				</div>
			</div>
		</div>
	{:else if variant === 'image'}
		<div
			class="hero min-h-[30vh] md:min-h-[40vh] lg:min-h-[50vh] {bannerClass ?? ''}"
			style={background?.style || ''}
		>
			{#if background?.overlay}
				{@render background.overlay()}
			{:else}
				<div class="hero-overlay bg-neutral/80"></div>
			{/if}
			<div class="hero-content text-neutral-content w-full max-w-5xl text-center">
				<div class="w-full md:max-w-2xl lg:max-w-3xl">
					{@render displayContent(content)}
				</div>
			</div>
		</div>
	{:else if variant === 'form'}
		<div class="hero bg-neutral text-neutral-content {bannerClass ?? ''}">
			<div
				class="hero-content w-full max-w-5xl flex-col gap-4 py-8 md:gap-6 md:py-12 lg:flex-row lg:gap-8 lg:py-16"
			>
				<div class="w-full text-center lg:w-1/2 lg:text-left">
					<h2 class="font-bold text-2xl mb-2 md:text-3xl md:mb-3 lg:text-4xl lg:mb-4">{content.mainMessage}</h2>
					{#if content.subMessage}
						<p class="text-base mb-4 md:text-lg md:mb-5 lg:text-xl lg:mb-6">{content.subMessage}</p>
					{/if}
					{#if content.buttonText && content.buttonHref}
						<div class="mt-4 flex justify-center md:mt-6 lg:justify-start">
							<a
								href={content.buttonHref}
								aria-label={content.buttonText}
								class="btn btn-primary group hover:scale-105 hover:brightness-110 active:scale-95 transition-all duration-300 shadow-md hover:shadow-lg btn-md md:btn-lg lg:btn-wide no-underline"
							>
								<span class="flex items-center gap-2">
									{content.buttonText}
									<ArrowRight
										class="inline-block h-5 w-5 transition-transform group-hover:translate-x-1"
									/>
								</span>
							</a>
						</div>
					{/if}
				</div>
				<div class="card bg-base-200 w-full max-w-sm flex-shrink-0 shadow-2xl lg:w-1/2">
					<div class="card-body p-4 md:p-6 lg:p-8">
						{#if form}
							{@render form({ email, handleSubmit })}
						{/if}
					</div>
				</div>
			</div>
		</div>
	{/if}
</svelte:boundary>
