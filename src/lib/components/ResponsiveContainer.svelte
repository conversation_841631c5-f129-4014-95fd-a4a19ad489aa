<script lang="ts">
import type { Snippet } from 'svelte';

/**
 * A responsive container using Tailwind’s responsive classes directly.
 */
let {
    children,
    class: className = ''
} = $props();
</script>

<div class={`flex flex-col p-4 gap-2 md:p-6 md:gap-4 lg:p-8 lg:gap-6 ${className}` }>
    <!-- Removed viewport indicator -->

    <!-- Main content -->
    <div class="flex-1">
        {@render children()}
    </div>

    <!-- Removed extra content conditional -->
</div>
