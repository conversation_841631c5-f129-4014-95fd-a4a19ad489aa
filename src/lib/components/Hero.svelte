<script lang="ts">
	/**
	 * Hero component with multiple variants and responsive design
	 * Follows daisyUI Hero pattern with added flexibility and Svelte 5 runes
	 * Fully responsive using Tailwind CSS classes without viewport utility dependencies
	 */
	import type { Snippet } from 'svelte';
	import type { Picture } from 'vite-imagetools';
	import EnhancedImage from './EnhancedImage.svelte';
	import executiveSuvBg from '$lib/assets/images/executive_black_suv_service.jpg'; // Note: No ?enhanced
	// import ThreeSunrise from './effects/ThreeSunrise.svelte'; // Commented out for now
	import CssParallaxBackground from './effects/CssParallaxBackground.svelte';
import RainbowStripesBackground from './effects/RainbowStripesBackground.svelte';
	import { animationStore } from '$lib/stores/animation';
	import { onMount, onDestroy, tick } from 'svelte';
	import { browser } from '$app/environment';

	type Variant = 'overlay' | 'centered' | 'figure' | 'figureReverse' | 'full';
	type ContentLayout = 'default' | 'centered' | 'split';
	type TextSize = 'default' | 'large' | 'small';
	type CTAStyle = 'default' | 'prominent' | 'subtle';
	type CTAPosition = 'default' | 'right' | 'center' | 'bottom';

	let {
		// Layout props
		variant = 'overlay',
		minHeight = 'min-h-[100dvh] min-h-[100svh] min-h-[100vh]', // Use dvh as primary for keyboard adjustment, with fallbacks
		bgClass = 'bg-base-200',
		rounded = false,
		_fullWidth = true,
		contentWidth = 'max-w-md',

		// Content styling props
		contentLayout = 'default',
		textSize = 'default',
		ctaStyle = 'prominent',
		ctaPosition = 'bottom', // Set ctaPosition to 'bottom' by default
		ctaHref = '',

		// Image props
		image,
		paddingTop = 'pt-24',
		paddingBottom = 'pb-8 md:pb-12', // Reduced bottom padding to allow more space for button positioning
		contentHeight = 'h-full',

		// Content snippets
		heading,
		content,
		cta,

		// Fallback for backward compatibility
		children,

		// Background effect prop
		backgroundEffect = null
	} = $props<{
		// Layout props
		variant?: Variant;
		minHeight?: string;
		bgClass?: string;
		rounded?: boolean;
		fullWidth?: boolean;
		contentWidth?: string;

		// Content styling props
		contentLayout?: ContentLayout;
		textSize?: TextSize;
		ctaStyle?: CTAStyle;
		ctaPosition?: CTAPosition;
		ctaHref?: string;

		// Image props
		image?: Picture; // Expect the enhanced object directly
		paddingTop?: string;
		paddingBottom?: string;
		contentHeight?: string;

		// Content snippets
		heading?: Snippet;
		content?: Snippet;
		cta?: Snippet;

		// Fallback for backward compatibility
		children?: Snippet;
		backgroundEffect?: 'sunrise' | 'css-parallax' | 'rainbow-stripes' | null; // Added 'rainbow-stripes'
	}>();

	// --- Parallax Effect Logic ---
	let heroRef: HTMLDivElement | null = $state(null);
	let heroInView = $state(true); // Make reactive with $state
	let scrollProgress = $state(0); // Make reactive with $state
	let observer: IntersectionObserver | null = $state(null);
	let isInitialRender = $state(true); // Track initial render state

	function clamp(val: number, min: number, max: number) {
		return Math.max(min, Math.min(val, max));
	}

	// Enhanced scroll handling with adaptive smoothing
	const SCROLL_SMOOTHING_MIN = 0.85; // Minimum smoothing for fast scrolls
	const SCROLL_SMOOTHING_MAX = 0.92; // Maximum smoothing for slow scrolls
	let lastScrollTime = $state(performance.now());
	let targetScrollProgress = $state(0);

	function updateScrollProgress() {
		if (!heroRef || !browser) return;

		const rect = heroRef.getBoundingClientRect();
		const currentTime = performance.now();
		const timeDelta = currentTime - lastScrollTime;

		// Calculate raw progress with improved clamping
		let rawProgress = 0;
		if (rect.top >= 0) {
			rawProgress = 0;
		} else if (rect.bottom <= 0) {
			rawProgress = 1;
		} else {
			// Enhanced progress calculation considering viewport height
			const viewportProgress = -rect.top / (window.innerHeight - rect.height);
			rawProgress = clamp(viewportProgress, 0, 1);
		}

		// Adaptive smoothing based on scroll speed
		const scrollSpeed = Math.abs(rawProgress - targetScrollProgress) / Math.max(0.016, timeDelta / 1000);
		const adaptiveSmoothing = SCROLL_SMOOTHING_MIN +
			(SCROLL_SMOOTHING_MAX - SCROLL_SMOOTHING_MIN) * (1 - clamp(scrollSpeed * 2, 0, 1));

		targetScrollProgress = rawProgress;

		// Skip smoothing on initial render
		if (isInitialRender) {
			scrollProgress = rawProgress;
			isInitialRender = false;
		} else {
			// Apply adaptive smoothing
			scrollProgress = scrollProgress * adaptiveSmoothing + rawProgress * (1 - adaptiveSmoothing);
		}

		// Update store and timing
		animationStore.setScrollProgress(scrollProgress);
		lastScrollTime = currentTime;
	}

	// Single mount handler for all scroll and visibility logic
	onMount(() => {
		if (!browser) return;

		// Wait for next tick to ensure heroRef is bound
		tick().then(() => {
			if (!heroRef) {
				console.warn('Hero: heroRef not available after tick');
				return;
			}

			// Set up IntersectionObserver with enhanced thresholds
			observer = new IntersectionObserver(
				(entries) => {
					const [entry] = entries;
					const isCurrentlyVisible = entry.isIntersecting;
					heroInView = isCurrentlyVisible;
					animationStore.setEnabled(isCurrentlyVisible);

					// Update scroll progress on visibility change
					if (isCurrentlyVisible) {
						updateScrollProgress();
					}
				},
				{
					threshold: [0, 0.1, 0.5, 0.9, 1.0], // More granular thresholds
					rootMargin: '20px' // Small margin to prevent sharp cutoffs
				}
			);

			// Start observing
			observer.observe(heroRef);

			// Initial update and start animation
			updateScrollProgress();
			animationFrameId = requestAnimationFrame(animationLoop);

			// Add passive scroll listener
			window.addEventListener('scroll', () => requestAnimationFrame(updateScrollProgress), { passive: true });

			return () => {
				// Clean up all resources
				if (observer && heroRef) observer.disconnect();
				window.removeEventListener('scroll', () => requestAnimationFrame(updateScrollProgress));

				// Cancel animation frame
				if (animationFrameId !== null) {
					cancelAnimationFrame(animationFrameId);
					animationFrameId = null;
				}
			};
		});

		// Add passive scroll listener
		window.addEventListener('scroll', () => requestAnimationFrame(updateScrollProgress), { passive: true });

		return () => {
			// Clean up all resources
			if (observer && heroRef) observer.disconnect();
			window.removeEventListener('scroll', () => requestAnimationFrame(updateScrollProgress));

			// Cancel animation frame
			if (animationFrameId !== null) {
				cancelAnimationFrame(animationFrameId);
				animationFrameId = null;
			}
		};
	});

	onDestroy(() => {
		if (observer && heroRef) observer.unobserve(heroRef);
		observer = null;
	});

	// No need for separate state, use the destructured prop directly

	// Using Tailwind responsive classes for all responsive behavior

	// Determine classes based on props and viewport
	const roundedClass = $derived(rounded ? 'rounded' : '');

	// Responsive image sizing using standard media queries
	const imageSizes = '(min-width: 1024px) 100vw, 100vw';

	// Determine if we should use named snippets or children
	const hasNamedSnippets = heading !== undefined || content !== undefined || cta !== undefined;

	// State for the hero element reference
	let heroElement: HTMLDivElement | null = $state(null);
	// Ensure heroRef and heroElement are synchronized
	$effect(() => {
		heroElement = heroRef;
	});

	// Remove duplicate scroll handler as it's replaced by updateScrollProgress

	// Remove duplicate intersection handler as it's now integrated in onMount

	// Use requestAnimationFrame for smoother animations
	let animationFrameId: number | null = $state(null);
	let lastScrollY = $state(0); // Track last scroll position

	// Smooth animation loop
	function animationLoop() {
		// Only update if scroll position has changed
		const currentScrollY = window.scrollY;
		if (currentScrollY !== lastScrollY || isInitialRender) {
			// Update scroll progress with smoothing
			updateScrollProgress();
			lastScrollY = currentScrollY;
		}

		// Continue the loop
		animationFrameId = requestAnimationFrame(animationLoop);
	}

	onMount(() => {
		if (!browser) return;

		// Set up all event handlers after next tick
		tick().then(() => {
			if (!heroRef) {
				console.warn('Hero: heroRef not available after tick');
				return;
			}

			// Set up IntersectionObserver with enhanced thresholds
			observer = new IntersectionObserver(
				(entries) => {
					const [entry] = entries;
					const isCurrentlyVisible = entry.isIntersecting;
					heroInView = isCurrentlyVisible;
					animationStore.setEnabled(isCurrentlyVisible);

					// Update scroll progress on visibility change
					if (isCurrentlyVisible) {
						updateScrollProgress();
					}
				},
				{
					threshold: [0, 0.1, 0.5, 0.9, 1.0], // More granular thresholds
					rootMargin: '20px' // Small margin to prevent sharp cutoffs
				}
			);

			observer.observe(heroRef);

			// Initial update and start animation
			updateScrollProgress();
			animationFrameId = requestAnimationFrame(animationLoop);

			// Add passive scroll listener
			window.addEventListener('scroll', () => requestAnimationFrame(updateScrollProgress), { passive: true });
		});

		return () => {
			// Clean up all resources
			if (observer) observer.disconnect();
			window.removeEventListener('scroll', () => requestAnimationFrame(updateScrollProgress));

			// Cancel animation frame
			if (animationFrameId !== null) {
				cancelAnimationFrame(animationFrameId);
				animationFrameId = null;
			}
		};
	});
</script>

<div class="w-full overflow-x-hidden">
	{#if variant === 'centered'}
		<div class="hero {minHeight} {roundedClass} {bgClass}">
			<div
				class="hero-content text-secondary-content flex items-center justify-center text-center py-16 md:py-20"
			>
				{#if hasNamedSnippets}
					<div class="flex flex-col items-center gap-6 {contentWidth}">
						{#if heading}
							{@render heading()}
						{/if}
						{#if content}
							<div
								class="mb-6 text-left max-w-prose transition-all duration-300"
class:text-neutral={backgroundEffect === 'rainbow-stripes'}
class:text-base-content={backgroundEffect !== 'rainbow-stripes'}
								class:prose={textSize === 'large'}
								class:text-xl={textSize === 'large'}
								class:lg:text-2xl={textSize === 'large'}
								class:xl:text-3xl={textSize === 'large'}
								class:max-w-2xl={textSize === 'large'}
								class:prose-p:max-w-[25ch]={textSize === 'large'}
								class:text-sm={textSize === 'small'}
								class:md:text-base={textSize === 'small'}
								class:text-base={textSize === 'default'}
								class:md:text-xl={textSize === 'default'}
							>
								{@render content()}
							</div>
						{/if}
						{#if cta}
							<div
								class="flex transition-all duration-300"
								class:self-start={ctaPosition === 'right' || ctaPosition === 'bottom'}
								class:self-center={ctaPosition === 'center'}
								class:mt-auto={ctaPosition === 'bottom'}
								class:md:self-start={ctaPosition === 'default'}
								class:justify-center={ctaPosition === 'center'}
								class:md:justify-start={ctaPosition !== 'center'}
							>
								<a
								href={ctaHref || 'https://book.mylimobiz.com/v4/arise'}
								class="btn btn-primary rounded-md shadow-lg hover:shadow-xl transition-shadow duration-300"
								class:btn-sm={textSize === 'small'}
								class:btn-md={textSize === 'default'}
								class:btn-lg={textSize === 'large'}
								class:btn-wide={ctaStyle === 'prominent' || ctaStyle === 'default'}
								>
								 {@render cta()}
								</a>
							</div>
						{/if}
					</div>
				{:else if children}
					<!-- Fallback for backward compatibility -->
					{@render children()}
				{:else}
					<div>Default centered content</div>
				{/if}
			</div>
		</div>
	{:else if variant === 'figure'}
		<!-- Hero with figure -->
		<div class="hero {minHeight} {roundedClass} {bgClass}">
			<div class="hero-content flex flex-col !p-0 lg:flex-row gap-8 lg:gap-12">
				{#if image}
					<div class="max-w-sm lg:max-w-lg">
						<EnhancedImage
							src={image}
							alt={image.alt}
							class="rounded-lg shadow-2xl"
							sizes="(min-width: 1024px) 800px, (min-width: 768px) 640px, 100vw"
							fetchpriority="high"
							width={800}
							height={600}
						/>
					</div>
				{/if}
				<div class="flex-1">
					{#if children}
						{@render children()}
					{:else}
						<div>Default figure content</div>
					{/if}
				</div>
			</div>
		</div>
	{:else if variant === 'figureReverse'}
		<!-- Hero with figure in reverse order -->
		<div class="hero {minHeight} {roundedClass} {bgClass}">
			<div class="hero-content flex flex-col !p-0 lg:flex-row-reverse gap-8 lg:gap-12">
				{#if image}
					<div class="max-w-sm lg:max-w-lg">
						<EnhancedImage
							src={image}
							alt={image.alt}
							class="rounded-lg shadow-2xl"
							sizes="(min-width: 1024px) 800px, (min-width: 768px) 640px, 100vw"
							fetchpriority="high"
							width={800}
							height={600}
						/>
					</div>
				{/if}
				<div class="flex-1">
					{#if children}
						{@render children()}
					{:else}
						<div>Default figure reverse content</div>
					{/if}
				</div>
			</div>
		</div>
	{:else if variant === 'full'}
		<!-- Hero with full width image -->
		<div class="hero {minHeight} {roundedClass} bg-neutral relative overflow-hidden" role="banner">
			<div class="absolute inset-0" aria-hidden="true">
				{#if image}
					<EnhancedImage
						src={image}
						alt={image.alt}
						class="h-full w-full object-cover object-[55%]"
						sizes="100vw"
						fetchpriority="high"
						width={1920}
						height={1080}
					/>
				{/if}
			</div>
			<div class="flex h-full w-full flex-col px-4">
				<div
					class="hero-content text-neutral-content relative z-10 flex h-full flex-col !p-0 ${contentWidth === 'w-full' ? 'max-w-none' : ''}"
				>
					<div
						class={`${contentWidth} flex h-full w-full flex-col pt-24 pb-2 md:max-w-none md:pt-24 `}
					>
						{#if hasNamedSnippets}
							<!-- Using the named snippets with our styling -->
							<div class="flex h-full flex-col justify-end">
								<!-- Heading section at the top -->
								{#if heading}
									<div
										class="mb-6 text-left max-w-prose transition-all duration-300"
class:text-neutral={backgroundEffect === 'rainbow-stripes'}
class:text-base-content={backgroundEffect !== 'rainbow-stripes'}
										class:prose={textSize === 'large'}
										class:text-xl={textSize === 'large'}
										class:lg:text-2xl={textSize === 'large'}
										class:xl:text-3xl={textSize === 'large'}
										class:max-w-2xl={textSize === 'large'}
										class:prose-p:max-w-[25ch]={textSize === 'large'}
										class:text-sm={textSize === 'small'}
										class:md:text-base={textSize === 'small'}
										class:text-base={textSize === 'default'}
										class:md:text-xl={textSize === 'default'}
									>
										{@render heading()}
									</div>
								{/if}

								<!-- Push content to bottom -->
								<div class="flex-grow"></div>

								<!-- Bottom section with content and CTA -->
								<div class="mb-6 flex flex-col">
									<!-- Content/paragraph section -->
									{#if content}
										<div
											class="text-white mb-2 text-left max-w-prose transition-all duration-300"
											class:prose={textSize === 'large'}
											class:text-xl={textSize === 'large'}
											class:lg:text-2xl={textSize === 'large'}
											class:xl:text-3xl={textSize === 'large'}
											class:max-w-2xl={textSize === 'large'}
											class:prose-p:max-w-[25ch]={textSize === 'large'}
											class:text-sm={textSize === 'small'}
											class:md:text-base={textSize === 'small'}
											class:text-base={textSize === 'default'}
											class:md:text-xl={textSize === 'default'}
										>
											{@render content()}
										</div>
									{/if}

									<!-- CTA section -->
									{#if cta}
										<div 
											class="flex flex-row items-center mt-auto pt-2 mb-4 md:mb-0 self-end sm:self-auto
											class:sm:self-start={ctaPosition === 'right' || ctaPosition === 'bottom'}
											class:sm:self-center={ctaPosition === 'center'}
											class:mt-auto={ctaPosition === 'bottom'}
											class:md:self-start={ctaPosition === 'default'}
											class:justify-center={ctaPosition === 'center'}
											class:md:justify-start={ctaPosition !== 'center'}"
										>
											<a
												href={ctaHref || 'https://book.mylimobiz.com/v4/arise'}
												class="btn btn-lg shadow-lg transform-gpu hover:scale-105 transition-transform duration-300
													class:btn-outline={ctaStyle === 'subtle'}
													class:btn-primary={ctaStyle === 'prominent'}
													class:btn-secondary={ctaStyle === 'default'}
													class:btn-accent={ctaStyle === 'subtle'}
													class:btn-wide={ctaStyle === 'prominent' || ctaStyle === 'default'}"
												style="will-change: transform;"
											>
												{@render cta()}
											</a>
										</div>
									{/if}
								</div>
							</div>
						{:else if children}
							<!-- Backward compatibility: just render children -->
							{@render children()}
						{:else}
							<div>Default full content</div>
						{/if}
					</div>
				</div>
			</div>
		</div>
	{:else if variant === 'overlay'}
		<!-- Hero with overlay -->
		<div
			class="relative w-full h-full overflow-visible {minHeight} {bgClass}"
			role="banner"
			bind:this={heroRef}
		>
			{#if backgroundEffect === 'sunrise'}
				<!-- Container for parallax elements with enhanced styling -->
				<div class="absolute inset-0 z-0 overflow-visible {bgClass}">
					<!-- Three.js Sunrise Background -->
					<!-- <ThreeSunrise /> --> <!-- Commented out for now -->
					<!-- Restore overlays -->
					<div class="absolute inset-0 z-10 bg-gradient-to-b from-transparent via-transparent to-base-100/80"></div>
					<div class="absolute inset-0 z-10 bg-[radial-gradient(ellipse_at_center,_transparent_60%,_rgba(0,0,0,0.3))] opacity-80"></div>
				</div>
			{:else if backgroundEffect === 'css-parallax'}
				<!-- Container for CSS Parallax elements -->
				<div class="absolute inset-0 z-0 overflow-hidden {bgClass}">
					<CssParallaxBackground />
					<!-- Optional: Add overlays similar to sunrise if needed -->
					<!-- <div class="absolute inset-0 z-10 bg-gradient-to-b from-transparent via-transparent to-base-100/80"></div> -->
					<!-- <div class="absolute inset-0 z-10 bg-[radial-gradient(ellipse_at_center,_transparent_60%,_rgba(0,0,0,0.3))] opacity-80"></div> -->
				</div>
{:else if backgroundEffect === 'rainbow-stripes'}
				<div class="absolute inset-0 z-0 overflow-hidden {bgClass}">
					<RainbowStripesBackground />
					<div class="absolute inset-0 z-10 bg-gradient-to-b from-transparent via-transparent to-base-100/80"></div>
				</div>
			{:else if image}
				<!-- Original image and overlay logic -->
				<div class="absolute inset-0" aria-hidden="true">
					<EnhancedImage
						src={image}
						alt={image.alt}
						class="h-full w-full object-cover object-[55%]"
						sizes="100vw"
						fetchpriority="high"
						width={1920}
						height={1080}
					/>
					<div
						class="hero-overlay {roundedClass} {bgClass ? bgClass + '/60' : 'bg-neutral/60'} absolute inset-0"
						aria-hidden="true"
					></div>
				</div>
			{/if}
			<div class="w-full px-4 md:px-6 {paddingTop} {paddingBottom} flex flex-col">
				<div
					class="hero-content relative z-10 !p-0 pb-16 text-left {contentHeight} transition-all duration-700"
class:text-neutral={backgroundEffect === 'rainbow-stripes'}
class:text-base-content={backgroundEffect !== 'rainbow-stripes'}
					style="opacity: {heroInView ? '1' : '0'}; transform: translateY({heroInView ? '0' : '10px'}); will-change: opacity, transform;"
				>
					<div class={`${contentWidth} flex w-full flex-col gap-8 md:max-w-none lg:gap-12`}>
						{#if hasNamedSnippets}
							<!-- Using named snippets -->
							<div class="flex h-full flex-col justify-end">
								<!-- Heading section with enhanced styling and hardware acceleration -->
								{#if heading}
									<div
										class="mb-6 text-left max-w-prose transition-all duration-300"
class:text-neutral={backgroundEffect === 'rainbow-stripes'}
class:text-base-content={backgroundEffect !== 'rainbow-stripes'}
										class:prose={textSize === 'large'}
										class:text-xl={textSize === 'large'}
										class:lg:text-2xl={textSize === 'large'}
										class:xl:text-3xl={textSize === 'large'}
										class:max-w-2xl={textSize === 'large'}
										class:prose-p:max-w-[25ch]={textSize === 'large'}
										class:text-sm={textSize === 'small'}
										class:md:text-base={textSize === 'small'}
										class:text-base={textSize === 'default'}
										class:md:text-xl={textSize === 'default'}
									>
										{@render heading()}
									</div>
								{/if}

								<!-- Content section with staggered animation and hardware acceleration -->
								{#if content}
									<div 
										class="mb-3 text-left md:text-left transform transition-all duration-700 delay-100 max-w-prose"
class:text-neutral={backgroundEffect === 'rainbow-stripes'}
class:text-base-content={backgroundEffect !== 'rainbow-stripes'}
										class:text-xl={textSize === 'large'}
										class:lg:text-2xl={textSize === 'large'}
										class:xl:text-3xl={textSize === 'large'}
										class:max-w-2xl={textSize === 'large'}
										class:prose-p:max-w-[25ch]={textSize === 'large'}
										class:text-sm={textSize === 'small'}
										class:md:text-base={textSize === 'small'}
										class:text-base={textSize === 'default'}
										class:md:text-xl={textSize === 'default'}
										style="transform: translateY({heroInView ? '0' : '20px'}); opacity: {heroInView ? '1' : '0.6'}; will-change: transform, opacity; backface-visibility: hidden;"
									>
										{@render content()}
									</div>
								{/if}

								<!-- CTA section with animation and hardware acceleration -->
								{#if cta}
									<div 
										class="flex flex-row items-center pt-4 mb-4 md:mb-0
  self-center justify-center
  class:md:self-end={ctaPosition === 'right'}
  class:md:justify-end={ctaPosition === 'right'}
  class:self-center={ctaPosition === 'center'}
  class:justify-center={ctaPosition === 'center'}
  class:md:self-start={ctaPosition === 'left'}
  class:md:justify-start={ctaPosition === 'left'}
"
										style="transform: translateY({heroInView ? '0' : '20px'}); opacity: {heroInView ? '1' : '0.6'}; will-change: transform, opacity; backface-visibility: hidden;"
									>
										<a
											href={ctaHref || 'https://book.mylimobiz.com/v4/arise'}
											class="btn rounded-md shadow-lg hover:shadow-xl transition-shadow duration-300
												class:btn-outline={ctaStyle === 'subtle'}
												class:btn-primary={ctaStyle === 'prominent'}
												class:btn-secondary={ctaStyle === 'default'}
												class:btn-accent={ctaStyle === 'subtle'}
												class:btn-sm={textSize === 'small'}
												class:btn-md={textSize === 'default'}
												class:btn-lg={textSize === 'large'}
												class:btn-wide={ctaStyle === 'prominent' || ctaStyle === 'default'}"
										>
											{@render cta()}
										</a>
									</div>
								{/if}
							</div>
						{:else if children}
							{@render children()}
						{:else}
							<div>Default overlay content</div>
						{/if}
					</div>
				</div>
			</div>
		</div>
	{:else}
		<!-- Default Hero with overlay -->
		<div class="hero {minHeight} bg-base-200" style={image ? `background-image: url(${image.src})` : ''}>
			<div class="hero-overlay bg-opacity-60"></div>
			<div class="hero-content text-neutral-content text-center">
				<div class="max-w-md">
					{#if children}
						{@render children()}
					{:else}
						<div>Default hero content</div>
					{/if}
				</div>
			</div>
		</div>
	{/if}
</div>
