<script lang="ts">
	import CardCarousel from './CardCarousel.svelte';
	import EnhancedImage from './EnhancedImage.svelte';
	import Prose from './Prose.svelte';

	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { fade, fly, slide } from 'svelte/transition';
	import { cubicOut } from 'svelte/easing';
	// Removed viewport imports - using Tailwind responsive classes instead
	import { prefersReducedMotion } from 'svelte/motion';
	import { ArrowRight, CarIcon } from 'lucide-svelte';
	import { onMount } from 'svelte';
	import type { Service } from '$lib/types/service';


	let { sectionTitle, cardItems }: { sectionTitle: string; cardItems: Service[] } = $props();

	// Tracking visibility states
	let sectionElement = $state<HTMLElement | null>(null);
	let isVisible = $state(false);
	let isClient = $derived(browser);

	// Using Tailwind responsive classes instead of viewport-derived values

	// User preference for reduced motion
	const userPrefersReducedMotion = prefersReducedMotion.current;

	// Use Intersection Observer to trigger animations when section is visible
	onMount(() => {
		if (!browser) return;

		const observer = new IntersectionObserver(
			(entries) => {
				const [entry] = entries;
				if (entry.isIntersecting) {
					isVisible = true;
					// Once triggered, no need to observe anymore
					if (sectionElement) observer.unobserve(sectionElement);
				}
			},
			{
				threshold: 0.2, // Trigger when 20% of the element is visible
				rootMargin: '0px 0px -100px 0px' // Adjust based on when you want the trigger to happen
			}
		);

		if (sectionElement) observer.observe(sectionElement);

		return () => {
			if (sectionElement) observer.unobserve(sectionElement);
		};
	});

	// Calculate animation delays
	const getHeaderDelay = () => (userPrefersReducedMotion ? 0 : 100);
	const getDividerDelay = () => (userPrefersReducedMotion ? 0 : 300);
	const getDescriptionDelay = () => (userPrefersReducedMotion ? 0 : 400);
</script>

{#snippet cardContent(item: Service)}
	<figure class="group overflow-hidden">
		<!-- TODO: Refine sizes based on actual card image display width -->
		<EnhancedImage
			src={item.imageSrc}
			alt={item.title}
			class="rounded-t-box h-52 w-full object-cover transition-transform duration-300 ease-out group-hover:scale-105"
			sizes="(min-width: 1024px) 800px, (min-width: 768px) 640px, 320px"
		/>
	</figure>
	<div class="px-6 pt-6">
		<h3 class="card-title mb-2 text-xl font-bold">{item.title}</h3>
	</div>
	<div class="text-base-content px-6">
		<Prose size="sm" color="neutral">
			<p class="line-clamp-4">{item.description}</p>
		</Prose>
	</div>
{/snippet}

{#snippet cardAction(item: Service)}
	<a
		href="https://book.mylimobiz.com/v4/arise"
		class="btn btn-primary group rounded-md"
		aria-label={`Reserve ${item.title}`}
	>
		Reserve
		<ArrowRight
			size={20}
			strokeWidth={1.5}
			class="ml-2 transition-transform duration-300 group-hover:translate-x-1"
			aria-hidden="true"
		/>
	</a>
{/snippet}

<section
	id="services"
	class="bg-neutral text-neutral-content w-full py-16 lg:py-24"
	bind:this={sectionElement}
>
	<div class="mx-auto w-full px-4">
		{#if isClient}
			<!-- Section header -->
			<div class="mb-12 text-center">
				{#if isVisible}
					<h2
						id="services-title"
						class="mb-4 text-3xl font-bold md:text-4xl"
						in:slide={{
							duration: userPrefersReducedMotion ? 100 : 600,
							delay: getHeaderDelay(),
							easing: cubicOut
						}}
					>
						{sectionTitle}
					</h2>

					<div
						class="divider mx-auto max-w-md"
						in:fade={{
							duration: userPrefersReducedMotion ? 100 : 400,
							delay: getDividerDelay()
						}}
					>
						<CarIcon
							class="text-primary animate-pulse duration-700"
							size={32}
							strokeWidth={1.5}
							aria-hidden="true"
						/>
					</div>

					<p
						class="mx-auto mt-4 max-w-2xl text-lg opacity-80"
						in:slide={{
							duration: userPrefersReducedMotion ? 100 : 600,
							delay: getDescriptionDelay(),
							easing: cubicOut
						}}
					>
						Experience the finest luxury transportation in Dallas with our premium SUV services
						tailored to your needs.
					</p>
				{/if}
			</div>

			<!-- Carousel -->
			{#if isVisible}
				<div in:fade={{ duration: 400, delay: getDescriptionDelay() + 200 }} class="mx-auto w-full">
					<CardCarousel
						items={cardItems}
						{cardContent}
						{cardAction}
						carouselClasses="mx-auto"
						sectionVisible={isVisible}
					/>
				</div>
			{/if}
		{:else}
			<!-- SSR fallback version -->
			<div class="mb-12 text-center opacity-0">
				<h2 id="services-title" class="mb-4 text-3xl font-bold md:text-4xl">{sectionTitle}</h2>
				<div class="divider mx-auto max-w-md">
					<CarIcon class="text-primary" size={32} strokeWidth={1.5} aria-hidden="true" />
				</div>
				<p class="mx-auto mt-4 max-w-2xl text-lg opacity-80">
					Experience the finest luxury transportation in Dallas with our premium SUV services
					tailored to your needs.
				</p>
			</div>

			<div class="opacity-0">
				<CardCarousel items={cardItems} {cardContent} {cardAction} />
			</div>
		{/if}
	</div>
</section>
