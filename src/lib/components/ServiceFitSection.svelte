<script lang="ts">
	import { fade, fly } from 'svelte/transition';
	import { Briefcase, GlassWater, Armchair, Users } from 'lucide-svelte';

	type IconType = 'Briefcase' | 'GlassWater' | 'Armchair' | 'Users';

	interface TimelineItem {
		title: string;
		description: string;
		icon: IconType;
	}

	let {
		sectionTitle = 'How We Fit Your Needs',
		backgroundClass = 'bg-base-100',
		items = [
			{
				icon: 'Briefcase' as IconType,
				title: 'BUSINESS TRAVELERS',
				description:
					'Arrive on time and refreshed for your meetings with our luxury vehicles and pro chauffeurs.'
			},
			{
				icon: 'GlassWater' as IconType,
				title: 'SPECIAL OCCASION PLANNERS',
				description:
					'Turn your event into something unforgettable with premium rides for you and your crew.'
			},
			{
				icon: 'Armchair' as IconType,
				title: 'COMFORT SEEKERS',
				description:
					'Kick back and enjoy the ride with our spacious, luxury vehicles for any personal trip.'
			},
			{
				icon: 'Users' as IconType,
				title: 'GROUP TRANSPORTATION',
				description:
					'Efficient solutions for groups of any size, from small teams to large parties.'
			}
		] as TimelineItem[],
		children = null
	} = $props();

	let isVisible = $state(false);
	let sectionRef = $state<HTMLElement | null>(null);

	$effect(() => {
		if (typeof window !== 'undefined' && sectionRef) {
			const observer = new IntersectionObserver(
				(entries) => {
					if (entries[0]?.isIntersecting) {
						isVisible = true;
						observer.disconnect();
					}
				},
				{ threshold: 0.1 }
			);

			observer.observe(sectionRef);

			return () => observer.disconnect();
		} else {
			isVisible = true;
		}
	});
</script>

<section
	bind:this={sectionRef}
	class={`py-12 ${backgroundClass}`}
	aria-labelledby="service-fit-title"
>
	<div class="container mx-auto px-4">
		<h2
			id="service-fit-title"
			class="mb-10 text-center text-3xl font-bold md:text-4xl"
			in:fade={{ duration: 400 }}
		>
			{sectionTitle}
		</h2>

		{#if isVisible}
			<!-- Timeline: Vertical on mobile/tablet, Horizontal on desktop (lg and up) -->
			<div class="mx-auto w-full max-w-5xl px-4">
				<ul class="timeline timeline-vertical lg:timeline-horizontal" role="list">
					{#each items as item, i}
						<li role="listitem" in:fly={{ x: i % 2 === 0 ? 20 : -20, duration: 300, delay: Math.min(i * 80, 300) }}>
							{#if i > 0}
								<hr class="bg-primary" />
							{/if}
							<!-- Mobile/Tablet: Alternating layout for vertical timeline -->
							<!-- Desktop: Standard layout for horizontal timeline -->
							{#if i % 2 === 0}
								<!-- Mobile/Tablet: Content on the right for even items -->
								<div class="timeline-start lg:hidden"></div>
							{/if}
							
							<div class="timeline-middle">
								<div
									class="bg-primary text-primary-content rounded-full p-2 lg:mx-auto lg:inline-flex"
									aria-hidden="true"
								>
									{#if item.icon === 'Briefcase'}
										<Briefcase size="24" strokeWidth="1.5" />
									{:else if item.icon === 'GlassWater'}
										<GlassWater size="24" strokeWidth="1.5" />
									{:else if item.icon === 'Armchair'}
										<Armchair size="24" strokeWidth="1.5" />
									{:else if item.icon === 'Users'}
										<Users size="24" strokeWidth="1.5" />
									{/if}
								</div>
							</div>
							
							<!-- Timeline box with content -->
							<div 
								class="timeline-box bg-base-200 rounded-box p-4 shadow-md
									{i % 2 === 0 ? 'timeline-end' : 'timeline-start lg:timeline-end'}
									lg:w-52"
							>
								<!-- Mobile/Tablet: Show title in a row -->
								<div class="lg:hidden mb-2 flex items-center gap-4 {i % 2 !== 0 ? 'flex-row-reverse' : ''}">
									<h3 class="text-base-content font-bold tracking-wider uppercase {i % 2 !== 0 ? 'text-right' : ''}">
										{item.title}
									</h3>
								</div>
								
								<!-- Desktop: Only show title (icon is in the middle) -->
								<h3 class="hidden lg:block text-base-content text-sm font-bold tracking-wider uppercase">
									{item.title}
								</h3>
								<p class="text-xs opacity-80 {i % 2 !== 0 ? 'lg:text-left text-right' : ''}">{item.description}</p>
							</div>
							
							{#if i % 2 !== 0}
								<!-- Mobile/Tablet: Empty div for odd items layout -->
								<div class="timeline-end lg:hidden"></div>
							{/if}
							
							{#if i < items.length - 1}
								<hr class="bg-primary" />
							{/if}
						</li>
					{/each}
				</ul>
			</div>
		{/if}

		{#if children}
			<div class="mt-12">{@render children()}</div>
		{/if}
	</div>
</section>
