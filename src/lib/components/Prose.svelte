<script lang="ts">
	import type { Snippet } from 'svelte';
	import { fade } from 'svelte/transition';

	/**
	 * A typography component that leverages Tailwind's Typography plugin for consistent text styling.
	 * Provides beautiful typographic defaults to any vanilla HTML content.
	 *
	 * Features:
	 * - Multiple size options for different content scales
	 * - Built-in color themes matching Tailwind's gray scales
	 * - Dark mode support via prose-invert
	 * - Element-specific modifier support
	 * - Max-width control for content readability
	 * - Loading and error states with transitions
	 *
	 * @component
	 * @example
	 * ```svelte
	 * <!-- Basic usage -->
	 * <Prose>
	 *   {#snippet children()}
	 *     <h1>Basic Title</h1>
	 *     <p>Regular paragraph text.</p>
	 *   {/snippet}
	 * </Prose>
	 *
	 * <!-- Advanced usage -->
	 * <Prose
	 *   size="xl"
	 *   color="slate"
	 *   invert={true}
	 *   class="prose-img:rounded-xl prose-a:text-primary"
	 * >
	 *   {#snippet children()}
	 *     <h1>Custom Article</h1>
	 *     <p class="lead">Leading paragraph...</p>
	 *   {/snippet}
	 * </Prose>
	 *
	 * <!-- Responsive typography -->
	 * <Prose class="prose-sm md:prose-base lg:prose-lg">
	 *   {#snippet children()}
	 *     <h1>Responsive Content</h1>
	 *     <p>Scales with viewport...</p>
	 *   {/snippet}
	 * </Prose>
	 * ```
	 */

	/**
	 * Typography size options
	 * @typedef {'sm' | 'base' | 'lg' | 'xl' | '2xl'} Size
	 *
	 * Sizes and their corresponding body font sizes:
	 * - 'sm': 0.875rem (14px)
	 * - 'base': 1rem (16px)
	 * - 'lg': 1.125rem (18px)
	 * - 'xl': 1.25rem (20px)
	 * - '2xl': 1.5rem (24px)
	 */
	type Size = 'sm' | 'base' | 'lg' | 'xl' | '2xl';

	/**
	 * Available gray scale color themes
	 * @typedef {'' | 'slate' | 'gray' | 'zinc' | 'neutral' | 'stone'} Color
	 *
	 * Each theme provides carefully designed colors for:
	 * - Text and headings
	 * - Links and bold text
	 * - Quotes and code blocks
	 * - Lists and horizontal rules
	 * - Tables and borders
	 * - And their dark mode variants
	 */
	type Color = '' | 'slate' | 'gray' | 'zinc' | 'neutral' | 'stone';

	/**
	 * Valid HTML elements for the container
	 * @typedef {'div' | 'article' | 'section' | 'main' | 'aside'} Element
	 */
	type Element = 'div' | 'article' | 'section' | 'main' | 'aside';

	/**
	 * Component props interface
	 * @typedef {Object} ProseProps
	 * @property {Size} [size='base'] - Typography size scale. Changes overall content size.
	 * @property {Color} [color=''] - Gray scale theme to use. Empty string uses default gray.
	 * @property {boolean} [invert=false] - Whether to use dark mode styles.
	 * @property {Element} [as='article'] - HTML element to render the container as.
	 * @property {string} [class=''] - Additional classes, useful for element modifiers like 'prose-img:rounded-xl'.
	 * @property {boolean} [maxWidth=true] - Whether to apply default max-width constraints for readability.
	 * @property {boolean} [loading=false] - Show loading state with spinner.
	 * @property {Snippet} children - Content to render with typography styles.
	 * @property {() => void} [onRendered] - Called after content is successfully rendered.
	 * @property {(err: Error) => void} [onError] - Called when an error occurs during rendering.
	 */
	interface ProseProps {
		size?: Size;
		color?: Color;
		invert?: boolean;
		as?: Element;
		class?: string;
		maxWidth?: boolean;
		loading?: boolean;
		children: Snippet;
		onRendered?: () => void;
		onError?: (err: Error) => void;
	}

	let {
		size = 'base',
		color = '', // Default to base gray theme
		invert = false,
		as = 'article', // More semantic default for content
		class: className = '',
		maxWidth = true,
		loading = false,
		children,
		onRendered = () => {},
		onError = () => {}
	} = $props();

	// Error state management
	let error = $state<Error | null>(null);

	/**
	 * Computes classes following typography plugin patterns:
	 * 1. Base 'prose' class first
	 * 2. Size modifier (if not base)
	 * 3. Color theme modifier
	 * 4. Dark mode modifier
	 * 5. Max-width override
	 * 6. Custom element modifiers
	 */
	const proseClasses = $derived.by(() => {
		const classes = ['prose']; // Base class must come first

		// Add size modifier if not base
		if (size !== 'base') {
			classes.push(`prose-${size}`);
		}

		// Add color theme if specified
		if (color) {
			classes.push(`prose-${color}`);
		}

		// Add dark mode support
		if (invert) {
			classes.push('prose-invert');
		}

		// Handle max-width override
		if (!maxWidth) {
			classes.push('max-w-none');
		}

		// Add any custom element modifiers
		if (className) {
			classes.push(className);
		}

		return classes.join(' ');
	});

	// Notify when content is rendered
	$effect(() => {
		if (!loading && !error) {
			onRendered();
		}
	});
</script>

{#if loading}
	<div class="prose-loading" role="status" transition:fade|local={{ duration: 150 }}>
		<div class="flex items-center justify-center p-4">
			<div
				class="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
			></div>
			<span class="sr-only">Loading content...</span>
		</div>
	</div>
{:else if error}
	<div class="prose-error" role="alert" transition:fade|local={{ duration: 150 }}>
		<div class="not-prose">
			<!-- Prevents prose styles from affecting error display -->
			<p>Failed to render content</p>
			<pre>{error.message}</pre>
		</div>
	</div>
{:else}
	<svelte:boundary
		onerror={(error, reset) => {
			onError(error as Error);
			error = error as Error;
		}}
	>
		<svelte:element this={as} class={proseClasses} transition:fade|local={{ duration: 200 }}>
			{@render children()}
		</svelte:element>
	</svelte:boundary>
{/if}

<style>
	/* These classes are outside prose context intentionally */
	.prose-error {
		color: rgb(220 38 38);
		padding: 1rem;
		border: 1px solid currentColor;
		border-radius: 0.375rem;
		margin: 1rem 0;
	}

	.prose-loading {
		padding: 2rem;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/* Ensure loading spinner color inherits from theme */
	:global([data-theme='dark']) .prose-loading {
		color: rgb(229 231 235);
	}
</style>
