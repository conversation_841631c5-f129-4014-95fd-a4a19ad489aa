<script lang="ts">
	import type { Snippet } from 'svelte';

	interface ContentCardProps {
		content?: Snippet;
		action?: Snippet;
		highlighted?: boolean;
	}

	let { content, action, highlighted = false }: ContentCardProps = $props();
</script>

<article
	class="card bg-base-100 h-full shadow-xl transition-all duration-200 {highlighted
		? 'ring-primary z-10 shadow-2xl ring-2'
		: 'hover:-translate-y-1 hover:shadow-2xl'}"
	data-highlighted={highlighted}
	data-testid="content-card"
>
	{#if content}
		<div>
			{@render content()}
		</div>
	{/if}

	<div class="card-body flex flex-col justify-between">
		{#if action}
			<div class="card-actions mt-auto justify-end">
				{@render action()}
			</div>
		{/if}
	</div>
</article>
