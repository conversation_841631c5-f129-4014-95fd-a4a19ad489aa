<script lang="ts">
	/**
	 * Header component that wraps the Navigation component.
	 * Acts as the main navigation container for the application.
	 * Features elegant transition effects based on scroll position.
	 *
	 * @component
	 * @example
	 * ```svelte
	 * <Header />
	 * ```
	 */

	import { goto } from '$app/navigation';
import * as Sentry from '@sentry/sveltekit';
	import { setContext, type Snippet } from 'svelte';
	import { browser } from '$app/environment';
	
	import { MenuIcon, X } from 'lucide-svelte';
	import { Spring } from 'svelte/motion';
	import { cubicOut, elasticOut } from 'svelte/easing';
	import { fade, slide, fly } from 'svelte/transition';
	// Logo is now in /static/images/svg.svg; use direct URL for <img> tags
	import EnhancedImage from '$lib/components/EnhancedImage.svelte'; // Import EnhancedImage

	let {
		logoText = 'AriseTransit',
		logoHref = '/',
		actionHref = 'https://book.mylimobiz.com/v4/arise',
		actionText = 'Book a Ride',
		phoneNumber = 'tel:+19453436785',
		navigation
	} = $props<{
		logoText?: string;
		logoHref?: string;
		actionHref?: string;
		actionText?: string;
		phoneNumber?: string;
		navigation?: Snippet;
	}>();

	// Improved animation using Svelte 5 Spring
	const SCROLL_THRESHOLD = 50;

	// Use Spring with gentler values for smoother animations
	const headerSpring = new Spring(0, {
		stiffness: 0.075, // Lower stiffness = smoother motion
		damping: 0.7 // Higher damping = less oscillation
	});

	// Drawer state for smooth animations
	let drawerOpen = $state(false);

	// Simplified scroll handler with Spring animations
	function handleScroll() {
		if (!browser) return;
		const scrollY = window.scrollY;

		// Update spring.target instead of directly changing state
		if (scrollY > SCROLL_THRESHOLD) {
			headerSpring.target = 1;
		} else {
			headerSpring.target = 0;
		}
	}

	// Set up scroll listener and initial state
	$effect(() => {
		if (!browser) return;

		// Set initial state based on current scroll position
		headerSpring.target = window.scrollY > SCROLL_THRESHOLD ? 1 : 0;
	});

	// All responsive logic is now handled via Tailwind classes only. No JS-based viewport checks remain.
// If you need to adjust button size or variant responsively, do so with Tailwind's responsive utilities.
// Remove any references to $isDesktop or related variables. state
	let headerState = $derived(headerSpring.current);
	let isScrolled = $derived(headerState > 0.5); // Simple boolean for layout changes
	// Use glass effect with smooth opacity transition based on scroll position
	let headerBg = $derived(() => {
		if (headerState < 0.05) {
			// At the top - subtle background for visibility
			return 'bg-base-100/20 backdrop-blur-[1px]';
		} else if (headerState < 0.5) {
			// Starting to scroll - increasing glass effect
			const opacity = Math.floor(30 + headerState * 50);
			return `bg-base-100/${opacity} backdrop-blur-sm shadow-sm`;
		} else {
			// Fully scrolled - stronger glass effect
			return `bg-base-100/80 backdrop-blur shadow-md`;
		}
	});
	// Ensure smooth transition of padding with fixed min-height to prevent layout shifts
	// Removed navPaddingX and logoSize derived variables - handled by Tailwind breakpoints
	let buttonVariant = $derived(headerState > 0.5 ? 'btn-ghost' : 'btn-primary');
	// Logo text color transition based on scroll state

	// Removed horizontal translations as they don't look good
	// Keep only the color transitions

	// Use the new derived store directly
	// No need to get it from context anymore
	// let isTabletView = $derived(viewport.isTablet()); // Removed
	// let displayLogoText = $derived(isDesktopView || isTabletView ? logoText : 'Arise'); // Removed - Replaced with CSS approach

	// Keep opacity spring-based for smoothness
	let buttonOpacity = $derived(`opacity: ${headerState > 0.5 ? 1 : 0.9};`);

	function navigateTo(path: string) {
		if (path.startsWith('tel:')) {
			window.location.href = path;
		} else {
			goto(path);
		}
	}

	// Enhanced drawer handling with animation state
	const closeDrawer = () => {
		drawerOpen = false;
		const drawerCheckbox = document.getElementById('mobile-drawer') as HTMLInputElement;
		if (drawerCheckbox) drawerCheckbox.checked = false;
	};

	const openDrawer = () => {
		drawerOpen = true;
		const drawerCheckbox = document.getElementById('mobile-drawer') as HTMLInputElement;
		if (drawerCheckbox) drawerCheckbox.checked = true;
	};

	// Track drawer checkbox changes
	function handleDrawerChange(e: Event) {
		const checkbox = e.target as HTMLInputElement;
		drawerOpen = checkbox.checked;
	}

	// Keyboard handlers for accessibility
	function handleMenuKeydown(e: KeyboardEvent) {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			openDrawer();
		}
	}

	function handleCloseKeydown(e: KeyboardEvent) {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			closeDrawer();
		}
	}

	// Set context for child components
	setContext('closeDrawer', closeDrawer);

	// Header element reference
	let headerElement = $state<HTMLElement | null>(null);

	$effect(() => {
		if (!browser) return;

		const drawerCheckbox = document.getElementById('mobile-drawer') as HTMLInputElement;
		if (!drawerCheckbox) return;

		const handleDrawerChange = () => {
			if (drawerCheckbox.checked) {
				document.documentElement.classList.add('drawer-toggle-checked');
			} else {
				document.documentElement.classList.remove('drawer-toggle-checked');
			}
		};

		drawerCheckbox.addEventListener('change', handleDrawerChange);

		return () => {
			drawerCheckbox.removeEventListener('change', handleDrawerChange);
		};
	});
</script>

<svelte:document onscroll={handleScroll} />

<header
	bind:this={headerElement}
	class="fixed top-0 z-[100] w-full {headerBg} transition-[background-color,backdrop-filter,box-shadow] duration-300 ease-in-out"
>
	<div class="mx-auto {isScrolled ? 'px-4' : 'px-6'} transition-all duration-300 ease-in-out">
		<!-- Following daisyUI drawer pattern -->
		<div class="drawer">
			<input
				id="mobile-drawer"
				type="checkbox"
				class="drawer-toggle"
				aria-label="Toggle mobile navigation menu"
				aria-controls="mobile-navigation"
				onchange={handleDrawerChange}
			/>

			<div class="drawer-content">
				<!-- Navbar with dynamic classes and spring animations -->
				<div
					class="navbar h-auto min-h-14 justify-between px-0 {isScrolled ? 'py-2' : 'py-4'} transition-all duration-300 ease-in-out"
				>
					<!-- Left section with hamburger menu on mobile -->
					<div class="flex flex-none items-center h-full">
						<!-- Hamburger menu button - shown only on smaller screens -->
						<button
							type="button"
							class="drawer-button btn btn-ghost btn-neutral hover:bg-neutral/20 border-0 btn-square md:hidden"
							aria-label="Open menu"
							aria-controls="mobile-navigation"
							aria-expanded={drawerOpen}
							onclick={openDrawer}
							onkeydown={handleMenuKeydown}
						>
							<MenuIcon size={24} strokeWidth={1.5} class="text-neutral" />
							<span class="sr-only">Menu</span>
						</button>
						
						<!-- Desktop logo - only visible on md and above -->
						<a
  href={logoHref}
  class="hidden md:flex items-center"
  aria-label="Arise Transit Home"
>
  <img
    src="/images/svg.svg"
    alt="Arise Transit Logo"
    class="h-12 w-auto max-w-[180px]"
    style="height:3rem"
    draggable="false"
    loading="eager"
    fetchpriority="high"
  />
  <span class="sr-only">Arise Transit</span>
</a>
					</div>

					<!-- Desktop Navigation section - centered and shown only on larger screens -->
					<div class="hidden flex-1 justify-center md:flex">
						{@render navigation()}
					</div>
					<!-- Spacer for mobile/tablet to push logo to the right -->
					<div class="flex-1 md:hidden"></div>

					<!-- Right section: Mobile logo on small screens, CTA button on larger screens -->
					<div class="flex flex-none items-center">
						<!-- Mobile logo - only visible below md -->
						<a
							href={logoHref}
							class="md:hidden flex items-center"
							aria-label="Arise Transit Home"
						>
							<img
								src="/images/svg.svg"
								alt="Arise Transit Logo"
								class="h-8 w-auto max-w-[120px]"
								style="height:2rem"
								draggable="false"
								loading="eager"
								fetchpriority="high"
							/>
							<span class="sr-only">Arise Transit</span>
						</a>
						
						<!-- Book Now button - only visible on md screens and above -->
						<a
							href={actionHref}
							class="hidden md:flex btn {buttonVariant} btn-sm md:btn-md lg:btn-lg xl:btn-xl no-underline transition-transform duration-300 ease-in-out scale-100 hover:scale-105"
							style={buttonOpacity}
						>
							{actionText}
						</a>
					</div>
				</div>
			</div>

			<!-- Mobile drawer side with Svelte transitions -->
			<div class="drawer-side z-[99]">
				<!-- Drawer overlay with fade transition -->
				<label for="mobile-drawer" aria-label="Close menu" class="drawer-overlay"></label>

				<!-- Drawer content with slide transition -->
				{#if drawerOpen}
					<div
						id="mobile-navigation"
						class="menu menu-compact bg-base-200 text-base-content min-h-full w-80 py-4 shadow-xl flex flex-col"
						transition:fly={{ x: -300, duration: 300, easing: cubicOut }}
						role="navigation"
						aria-label="Mobile navigation"
					>
						<!-- Drawer header with close button -->
						<div class="mb-4 flex items-center justify-end">
							<button
								type="button"
								class="btn btn-ghost btn-circle"
								aria-label="Close menu"
								onclick={closeDrawer}
								onkeydown={handleCloseKeydown}
							>
								<X size={24} strokeWidth={1} />
								<span class="sr-only">Close</span>
							</button>
						</div>

						<!-- Navigation menu with staggered fade-in transitions -->
						<div class="mt-2 flex-1" transition:fade={{ duration: 200, delay: 150 }}>
							{@render navigation()}
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</header>
