<script lang="ts">
	import type { Picture } from 'vite-imagetools';

	// Keep the props interface the same
	interface EnhancedImageProps {
		src: string | (Picture & { src: string });
		alt: string;
		class?: string;
		sizes?: string;
		fetchpriority?: 'high' | 'low' | 'auto';
		width?: number;
		height?: number;
	}

	// Destructure props directly
	let {
		src,
		alt,
		class: className = '',
		sizes = '100vw',
		fetchpriority = 'auto' as const,
		width = undefined,
		height = undefined
	} = $props();

	// Keep loading and error states
	let error = $state(false);
	let loading = $state(true);

	function handleLoad() {
		loading = false;
	}

	function handleError() {
		// Log the error for debugging
		console.error(`EnhancedImage failed to load: ${typeof src === 'string' ? src : src?.src}`, { alt });
		error = true;
		loading = false; // Stop showing loading skeleton on error
	}
</script>

<div class="relative h-full w-full">
	{#if !error}
		<enhanced:img
			src={src}
			{alt}
			class={`${className} ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
			{sizes}
			loading={fetchpriority === 'high' ? 'eager' : 'lazy'}
			{fetchpriority}
			
			onload={handleLoad}
			onerror={handleError}
		/>
	{/if}

	{#if loading || error}
		<div class="skeleton absolute inset-0 {className} bg-transparent" role="img" aria-label={alt}>
			<div class="sr-only">
				{#if error}
					Failed to load image: {alt}
				{:else}
					Loading {alt}
				{/if}
			</div>
		</div>
	{/if}
</div>
