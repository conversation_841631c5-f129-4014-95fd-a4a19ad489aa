<script lang="ts" generics="T extends { id: string | number }">
	import type { Snippet } from 'svelte';
	import ContentCard from './ContentCard.svelte';
	import { browser } from '$app/environment';
	import { fly, fade } from 'svelte/transition';
	import { cubicOut } from 'svelte/easing';
	import { prefersReducedMotion } from 'svelte/motion';
	import { ChevronLeft, ChevronRight } from 'lucide-svelte';
	import { tick } from 'svelte';

	interface CardCarouselProps<T extends { id: string | number }> {
		items: T[];
		cardContent: Snippet<[item: T]>;
		cardAction: Snippet<[item: T]>;
		carouselClasses?: string;
		sectionVisible?: boolean;
		containerClasses?: string; // For responsive container sizing
		cardClasses?: string; // For responsive card sizing
		spaceClasses?: string; // For responsive spacing
	}

	let {
		items,
		cardContent,
		cardAction,
		carouselClasses = '',
		sectionVisible = false,
		containerClasses = 'max-w-5xl md:max-w-6xl lg:max-w-7xl',
		cardClasses = 'w-80 md:w-[290px]',
		spaceClasses = 'space-x-6'
	}: CardCarouselProps<T> = $props();

	const isClient = $derived(browser);
	const userPrefersReducedMotion = prefersReducedMotion.current;

	// State management
	let activeItem = $state(0);
	let revealed = $state(sectionVisible);
	let carouselElement: HTMLElement;

	// Update reveal state when section visibility changes
	$effect(() => {
		revealed = sectionVisible;
	});

	// Handler for slide navigation
	async function goToSlide(index: number) {
		if (index === activeItem) return;
		activeItem = index;

		if (carouselElement && browser) {
			await tick();
			const slideElement = document.getElementById(`slide-${index}`);
			if (slideElement) {
				// Calculate scroll offset to center the slide
				const carouselRect = carouselElement.getBoundingClientRect();
				const slideRect = slideElement.getBoundingClientRect();
				const centerOffset = (carouselRect.width - slideRect.width) / 2;

				carouselElement.scrollTo({
					left: slideElement.offsetLeft - centerOffset,
					behavior: userPrefersReducedMotion ? 'auto' : 'smooth'
				});
			}
		}
	}

	// Navigate to previous slide
	function prevSlide() {
		const prev = activeItem === 0 ? items.length - 1 : activeItem - 1;
		goToSlide(prev);
	}

	// Navigate to next slide
	function nextSlide() {
		const next = activeItem === items.length - 1 ? 0 : activeItem + 1;
		goToSlide(next);
	}

	// Calculate animation delay with staggered effect
	function getDelay(index: number) {
		if (userPrefersReducedMotion) return 0;
		// Create a more pronounced staggered effect
		return Math.min(150 + index * 100, 750);
	}
</script>

<!-- Mobile View: Stacked Cards -->
<div class="md:hidden space-y-6 px-4">
	{#each items as item, index (item.id)}
		{#if revealed}
			<div
				in:fly={{
					y: 40,
					duration: userPrefersReducedMotion ? 200 : 600,
					delay: getDelay(index),
					easing: cubicOut
				}}
			>
				<ContentCard>
					{#snippet content()}
						{@render cardContent(item)}
					{/snippet}
					{#snippet action()}
						{@render cardAction(item)}
					{/snippet}
				</ContentCard>
			</div>
		{/if}
	{/each}
</div>

<!-- Desktop View: Responsive width carousel -->
<div class="hidden md:block relative {containerClasses} mx-auto pb-16">
	<!-- Main carousel -->
	<div
		bind:this={carouselElement}
		class="carousel carousel-center w-full {spaceClasses} p-4 {carouselClasses}"
	>
		{#each items as item, index (item.id)}
			{#if revealed}
				<div
					id={`slide-${index}`}
					class="carousel-item {cardClasses} flex-shrink-0"
					in:fade={{
						duration: userPrefersReducedMotion ? 200 : 500,
						delay: getDelay(index),
						easing: cubicOut
					}}
				>
					<ContentCard highlighted={index === activeItem}>
						{#snippet content()}
							{@render cardContent(item)}
						{/snippet}
						{#snippet action()}
							{@render cardAction(item)}
						{/snippet}
					</ContentCard>
				</div>
			{/if}
		{/each}
	</div>

	<!-- Navigation controls -->
	{#if items.length > 1 && revealed}
		<!-- Prev/Next buttons positioned for reliability and visibility -->
		<div
			class="absolute top-1/2 right-4 left-4 z-10 flex -translate-y-1/2 transform justify-between"
		>
			<button
				class="btn btn-circle bg-primary text-primary-content border-primary hover:bg-primary-focus shadow-xl transition-all duration-200"
				aria-label="Previous slide"
				onclick={prevSlide}
			>
				<ChevronLeft size={20} strokeWidth={1.5} />
			</button>

			<button
				class="btn btn-circle bg-primary text-primary-content border-primary hover:bg-primary-focus shadow-xl transition-all duration-200"
				aria-label="Next slide"
				onclick={nextSlide}
			>
				<ChevronRight size={20} strokeWidth={1.5} />
			</button>
		</div>

		<!-- Indicator dots -->
		<div class="mt-4 flex w-full justify-center gap-2 py-4">
			{#each items as _, index}
				<button
					class="btn btn-xs flex min-h-4 min-w-4 items-center justify-center rounded-full p-0 transition-all duration-200 {activeItem ===
					index
						? 'btn-primary'
						: 'btn-ghost bg-base-300 hover:bg-primary/60'}"
					aria-label={`Go to slide ${index + 1}`}
					aria-current={activeItem === index ? 'true' : undefined}
					onclick={() => goToSlide(index)}
				>
					{index + 1}
				</button>
			{/each}
		</div>
	{/if}
</div>
