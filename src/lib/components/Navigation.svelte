<script lang="ts">
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { NavItem } from '$lib/types';
	import { getContext } from 'svelte';
	// Removed svelte/transition and svelte/easing imports
	import { Phone } from 'lucide-svelte'; // Import Phone icon from Lucide

	/**
	 * Navigation component using DaisyUI's Menu component.
	 * Provides a responsive navigation interface that adapts between desktop and mobile views.
	 * Enhanced with Svelte 5 transitions for smooth animations.
	 */
	let {
		items = [],
		logoText = 'AriseTransit',
		logoHref = '/',
		actionHref = 'https://book.mylimobiz.com/v4/arise',
		actionText = 'Book Now',
		phoneNumber = 'tel:+19453436785',
		// Removed mode and onClose props
	} = $props<{
		items?: NavItem[];
		logoText?: string;
		logoHref?: string;
		actionHref?: string;
		actionText?: string;
		phoneNumber?: string;
		// Removed mode prop type
	}>();

	// State management with runes
	let focusedIndex = $state(-1);
	let hoveredIndex = $state(-1);

	// Calculate active item
	let activeItem = $derived(items.find((item: NavItem) => item.href === page.url.pathname));

	// Removed getStaggerDelay function

	function handleKeyDown(event: KeyboardEvent) {
		switch (event.key) {
			case 'ArrowDown':
			case 'ArrowRight':
				event.preventDefault();
				focusedIndex = (focusedIndex + 1) % items.length;
				focusMenuItem();
				break;
			case 'ArrowUp':
			case 'ArrowLeft':
				event.preventDefault();
				focusedIndex = focusedIndex <= 0 ? items.length - 1 : focusedIndex - 1;
				focusMenuItem();
				break;
			case 'Home':
				event.preventDefault();
				focusedIndex = 0;
				focusMenuItem();
				break;
			case 'End':
				event.preventDefault();
				focusedIndex = items.length - 1;
				focusMenuItem();
				break;
		}
	}

	function focusMenuItem() {
		const menuItem = document.querySelector(`[data-index="${focusedIndex}"]`) as HTMLElement;
		menuItem?.focus();
	}

	function handleError(error: unknown, reset: () => void) {
		console.error('Navigation error:', error);
		focusedIndex = -1;
		reset();
	}

	// Handle hover state for animation enhancements
	function handleMouseEnter(index: number) {
		hoveredIndex = index;
	}

	function handleMouseLeave() {
		hoveredIndex = -1;
	}

	// Get closeDrawer function from context (provided by Header)
	const contextCloseDrawer = getContext<() => void>('closeDrawer');

	function navigateTo(path: string) {
		if (path.startsWith('tel:')) {
			window.location.href = path;
		} else {
			goto(path);
		}

		// Close drawer logic is now fully handled in Header.svelte via context or direct call
		// We still call the context function if available, as it's the primary mechanism
		if (contextCloseDrawer) contextCloseDrawer();
	}
</script>

<svelte:window onkeydown={handleKeyDown} />

<svelte:boundary onerror={handleError}>
	<nav aria-label="Main navigation" class="navbar">
		<!-- Desktop Menu (Horizontal) - Hidden on small screens -->
		<ul class="menu menu-horizontal hidden md:flex gap-2 mx-auto items-center"
			role="menubar"
		>
			{#each items as { href, label }, i}
				<li
					role="none"
				>
					<a
						{href}
						class="{href === page.url.pathname
							? 'bg-base-300'
							: 'hover:bg-base-200'}"
						role="menuitem"
						data-index={i}
						tabindex={focusedIndex === i ? 0 : -1}
						onclick={() => navigateTo(href)}
						onmouseenter={() => handleMouseEnter(i)}
						onmouseleave={handleMouseLeave}
					>
						{label}
					</a>
				</li>
			{/each}
		</ul>

		<!-- Mobile Menu (Vertical) - Shown only on small screens (within drawer) -->
		<ul class="menu gap-2 md:hidden w-full p-0 min-w-0 max-w-full" role="menubar">
			<!-- Primary CTA: Book Now -->
			<li role="none" class="mb-2">
				<a role="menuitem"
					href={actionHref}
					class="btn btn-primary w-full no-underline transition-transform hover:scale-105"
					onclick={() => navigateTo(actionHref)}
				>
					{actionText}
				</a>
			</li>

			<!-- Secondary CTA: Call now -->
			<li role="none" class="mb-4">
				<a role="menuitem"
					href={phoneNumber}
					class="btn btn-secondary btn-outline flex w-full items-center justify-center gap-2 no-underline transition-transform hover:scale-105"
					onclick={() => navigateTo(phoneNumber)}
				>
					<Phone size={20} strokeWidth={1.5} />
					Call now
				</a>
			</li>

			{#each items as { href, label }, i}
				<li
					class={`flex items-center cursor-pointer w-full py-4 px-2 capitalize transition-all relative
					${href === page.url.pathname
						? 'bg-primary/10 text-primary font-semibold border-l-4 border-primary pointer-events-none'
						: 'hover:bg-base-200 focus-within:bg-base-200 hover:text-primary focus-within:text-primary hover:font-semibold focus-within:font-semibold'}
					`}
					role="none"
				>
					<a
						{href}
						class="block w-full h-full"
						role="menuitem"
						data-index={i}
						tabindex={focusedIndex === i ? 0 : -1}
						onclick={() => navigateTo(href)}
					>
						{label}
					</a>
				</li>
			{/each}
		</ul>
	</nav>
</svelte:boundary>
