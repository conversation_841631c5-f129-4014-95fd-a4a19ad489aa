import { PostHog } from 'posthog-node';
import { PUBLIC_POSTHOG_KEY } from '$env/static/public'; // Import the public static env var

let posthogClient: PostHog | null = null;

export function getPostHogClient(): PostHog | null {
	// Check if the key exists and the client hasn't been initialized yet
	if (PUBLIC_POSTHOG_KEY && !posthogClient) {
		posthogClient = new PostHog(PUBLIC_POSTHOG_KEY, {
			host: 'https://us.i.posthog.com'
			// You can add more server-side configuration options here
		});
		console.log('PostHog Node client initialized'); // Optional confirmation log
	} else if (!PUBLIC_POSTHOG_KEY && !posthogClient) {
		// Log a warning if the key is missing only once
		console.warn('PUBLIC_POSTHOG_KEY not found. Server-side PostHog analytics disabled.');
		// Set client to null or a dummy object to prevent errors if needed,
		// but returning null is often clearer.
	}
	return posthogClient;
}

// Optional: Graceful shutdown handling
// This might be more complex depending on your deployment environment (Node vs Serverless)
// For now, we'll rely on calling shutdown() manually after captures.
// process.on('exit', () => {
//   posthogClient?.shutdown();
// });