import { drizzle, type PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema'; // Import the schema definitions
import { DATABASE_URL } from '$env/static/private'; // Use static import for prerendering compatibility

// Keep a singleton instance to avoid creating multiple connections
let dbInstance: PostgresJsDatabase<typeof schema> | null = null;

/**
 * Gets the Drizzle database instance.
 * Initializes the connection on the first call.
 * Reads DATABASE_URL from dynamic private env vars at runtime.
 */
export async function getDb(): Promise<PostgresJsDatabase<typeof schema>> { // Keep async
  if (dbInstance) {
    return dbInstance;
  }

  // DATABASE_URL is now imported directly from static environment

  // Check if DATABASE_URL exists
  if (!DATABASE_URL) {
    // Throw error during runtime if DATABASE_URL is missing
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // Create database connection
  // Note: prepare: false might be needed depending on the environment (e.g., serverless)
  const client = postgres(DATABASE_URL, { prepare: false });

  // Initialize Drizzle with the client and schema
  dbInstance = drizzle(client, { schema });
  return dbInstance;
}
