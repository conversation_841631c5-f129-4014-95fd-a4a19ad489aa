import { pgTable, serial, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

export const contacts = pgTable('contacts', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull(),
  phone: varchar('phone', { length: 20 }), // Allow some length for formatting
  message: text('message').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true })
    .default(sql`CURRENT_TIMESTAMP`) // Automatically set timestamp on creation
    .notNull(),
});

// Optional: Define a type for inserting new contacts (useful for type safety)
export type NewContact = typeof contacts.$inferInsert;
// Optional: Define a type for selecting contacts (useful for type safety)
export type Contact = typeof contacts.$inferSelect;
