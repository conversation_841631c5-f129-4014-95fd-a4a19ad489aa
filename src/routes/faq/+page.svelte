<script lang="ts">
	import Prose from '$lib/components/Prose.svelte';
	import ResponsiveContainer from '$lib/components/ResponsiveContainer.svelte';
	import Hero from '$lib/components/Hero.svelte';
	import { isDesktop } from '$lib/utils/viewport.svelte';
	import { Accessibility, Info, HelpCircle } from 'lucide-svelte';

	// Use viewport utility consistently as per CLAUDE.md guidelines
	const isDesktopView = $derived($isDesktop);

	// Define type for FAQ items
	type FAQItem = {
		question: string;
		answer: string;
		category: CategoryKey;
	};

	// Define valid category keys
	type CategoryKey =
		| 'booking'
		| 'payment'
		| 'service'
		| 'policy'
		| 'complaints'
		| 'accessibility'
		| 'business';

	// Define type for category
	type Category = {
		title: string;
		faqs: FAQItem[];
	};

	// Define type for categories collection
	type Categories = {
		[key in CategoryKey]: Category;
	};

	// FAQ data structure
	const faqs: FAQItem[] = [
		{
			question: 'How do I book a ride?',
			answer:
				'You can book a ride through our online reservation system, by calling our dispatch at (*************, or <NAME_EMAIL>. We recommend booking at least 24 hours in advance to ensure availability.',
			category: 'booking'
		},
		{
			question: 'What payment methods do you accept?',
			answer:
				'We accept all major credit cards, corporate accounts, and cash payments. Payment can be made in advance through our online system or at the time of service.',
			category: 'payment'
		},
		{
			question: 'How far in advance should I book?',
			answer:
				"For standard service, we recommend booking at least 24 hours in advance. For airport pickups and special events, 48-72 hours' notice is preferred. Last-minute bookings are accommodated based on availability.",
			category: 'booking'
		},
		{
			question: 'What is your cancellation policy?',
			answer:
				'Cancellations made more than 24 hours before scheduled service will receive a full refund. Cancellations within 24 hours of service will incur a 50% cancellation fee. No-shows will be charged the full fare amount.',
			category: 'policy'
		},
		{
			question: 'Do you provide accessibility services?',
			answer:
				'Yes, wheelchair accessible vehicles are available upon request at standard rates. Please specify your accessibility needs when booking to ensure we provide the appropriate vehicle and assistance.',
			category: 'accessibility'
		},
		{
			question: 'How do I file a complaint about your service?',
			answer:
				'You can file a complaint directly with us via our contact form, <NAME_EMAIL>, or by calling (*************. For regulatory complaints, you can also call the DFW transportation hotline at (*************.',
			category: 'complaints'
		},
		{
			question: 'What are your service areas?',
			answer:
				'We serve the entire Dallas-Fort Worth metroplex, including Dallas, Fort Worth, Plano, Arlington, Irving, and surrounding suburbs. Long-distance service to Austin, Houston, and San Antonio is available by special arrangement.',
			category: 'service'
		},
		{
			question: 'Do you offer corporate accounts?',
			answer:
				'Yes, we offer corporate accounts with customized billing, priority booking, and volume discounts. Contact our corporate sales <NAME_EMAIL> for details.',
			category: 'business'
		},
		{
			question: 'What is included in the fare?',
			answer:
				"Our fares include the driver's time, fuel, standard amenities (bottled water, WiFi), and all applicable taxes. A 20% gratuity is automatically added to all fares for your convenience.",
			category: 'payment'
		},
		{
			question: 'What happens if my flight is delayed?',
			answer:
				"For airport pickups, we monitor flight status in real-time and adjust your pickup time accordingly at no additional charge. Our wait time begins 15 minutes after your flight has landed and you've cleared customs/immigration.",
			category: 'service'
		}
	];

	// Group FAQs by category
	const categories: Categories = {
		booking: { title: 'Booking Information', faqs: [] },
		payment: { title: 'Payment & Pricing', faqs: [] },
		service: { title: 'Services & Coverage', faqs: [] },
		policy: { title: 'Policies', faqs: [] },
		complaints: { title: 'Complaints & Feedback', faqs: [] },
		accessibility: { title: 'Accessibility', faqs: [] },
		business: { title: 'Business Services', faqs: [] }
	};

	// Populate categories
	faqs.forEach((faq) => {
		categories[faq.category].faqs.push(faq);
	});
</script>

<svelte:head>
	<title>Frequently Asked Questions - Arise Transit</title>
	<meta
		name="description"
		content="Find answers to common questions about Arise Transit's services, booking, payment, and policies."
	/>
</svelte:head>

<div class="mb-8">
	<Hero
		variant="overlay"
		minHeight="min-h-[40vh]"
		contentWidth="max-w-4xl"
		contentLayout="centered"
		textSize="large"
		ctaHref="#booking"
		bgClass="bg-neutral"
		backgroundEffect="rainbow-stripes"
	>
		{#snippet heading()}
			<h1 class="mb-4 text-4xl font-bold md:text-5xl text-neutral">Frequently Asked Questions</h1>
		{/snippet}

		{#snippet content()}
			<p class="text-xl">Find answers to your questions about our transportation services</p>
		{/snippet}

		{#snippet cta()}
			<span>Browse FAQs</span>
		{/snippet}
	</Hero>
</div>

<main class="py-8">
	<ResponsiveContainer class="mx-auto max-w-4xl">
		{#snippet children()}
			<Prose as="article" size="xl">
				<p class="lead">
					Below you'll find answers to the most common questions about our services. If you can't
					find what you're looking for, please contact our team directly.
				</p>
				<div class="my-6 flex justify-center">
					<a href="/contact" class="btn btn-outline no-underline">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="1.5"
							stroke="currentColor"
							class="mr-2 h-5 w-5"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"
							/>
						</svg>
						Contact Our Support Team
					</a>
				</div>

				<!-- FAQs by Category -->
				{#each Object.entries(categories) as [key, category]}
					{#if category.faqs.length > 0}
						<h2 id={key}>{category.title}</h2>
						<div class="join join-vertical w-full">
							{#each category.faqs as faq, i}
								<div class="collapse-arrow join-item border-base-300 collapse border">
									<input type="radio" name="faq-accordion-{key}" checked={i === 0} />
									<div class="collapse-title text-xl font-medium">
										{faq.question}
									</div>
									<div class="collapse-content">
										<p>{faq.answer}</p>
									</div>
								</div>
							{/each}
						</div>
					{/if}
				{/each}

				<div class="divider my-12"></div>

				<h2>Still Have Questions?</h2>
				<p>
					If you couldn't find the answer to your question, please don't hesitate to contact us. Our
					customer support team is available 24/7 to assist you.
				</p>

				<div class="mt-6 flex flex-wrap gap-4">
					<a href="https://book.mylimobiz.com/v4/arise" class="btn btn-primary">Book a Ride</a>
					<a href="/contact" class="btn">Contact Us</a>
				</div>

				<!-- Regulatory Complaints Section -->
				<div class="divider my-8"></div>

				<h2 id="regulatory-complaints">Regulatory Complaints</h2>
				<p>
					While we strive to provide excellent service, we understand that sometimes issues may
					arise. If you've experienced a problem with our service that requires regulatory
					attention:
				</p>

				<div class="alert bg-base-200 my-4">
					<Info class="stroke-info h-6 w-6 flex-shrink-0" />
					<div>
						<p class="font-medium">
							To file a regulatory transportation complaint in the DFW area:
						</p>
						<ul class="mt-2 list-inside list-disc">
							<li>Call the DFW Regional Transportation Hotline: (*************</li>
							<li>You will receive a complaint tracking number for your reference</li>
						</ul>
						<p class="mt-2 text-sm">In compliance with Ordinance 29596 (effective 4/30/15)</p>
					</div>
				</div>

				<!-- Accessibility Notice -->
				<div class="alert alert-info mt-12">
					<Accessibility class="h-6 w-6 shrink-0 stroke-current" />
					<div>
						<h3 class="font-bold">Wheelchair Accessible Vehicles</h3>
						<p>Wheelchair accessible vehicles are available upon request.</p>
					</div>
				</div>
			</Prose>
		{/snippet}
	</ResponsiveContainer>
</main>
