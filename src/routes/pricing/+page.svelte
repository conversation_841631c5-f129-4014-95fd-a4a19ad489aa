<script lang="ts">
	import Prose from '$lib/components/Prose.svelte';
	import ResponsiveContainer from '$lib/components/ResponsiveContainer.svelte';
	import Hero from '$lib/components/Hero.svelte';
	import { isDesktop } from '$lib/utils/viewport.svelte';
	import { Accessibility } from 'lucide-svelte';
	import { goto } from '$app/navigation';

	// Use viewport utility consistently as per CLAUDE.md guidelines
	const isDesktopView = $derived($isDesktop);
</script>

<svelte:head>
	<title>Pricing & Rates - Arise Transit</title>
	<meta
		name="description"
		content="Arise Transit's pricing and rate information for our transportation services."
	/>
</svelte:head>

<div class="mb-8">
	<Hero
		variant="overlay"
		minHeight="min-h-[40vh]"
		contentWidth="max-w-4xl"
		contentLayout="centered"
		textSize="large"
		bgClass="bg-neutral"
		backgroundEffect="rainbow-stripes"
	>
		{#snippet heading()}
			<h1 class="mb-4 text-4xl font-bold md:text-5xl text-neutral">Pricing & Rates</h1>
		{/snippet}

		{#snippet content()}
			<p class="text-xl">Transparent pricing for all your transportation needs</p>
		{/snippet}

		{#snippet cta()}
			<span>Learn More</span>
		{/snippet}
	</Hero>
</div>

<main class="py-8">
	<ResponsiveContainer class="mx-auto max-w-4xl">
		{#snippet children()}
			<Prose as="article" size="xl">
				<h2>Standard Rates</h2>

				<div class="overflow-x-auto">
					<table class="table-zebra table w-full">
						<thead>
							<tr>
								<th>Service Type</th>
								<th>Base Rate</th>
								<th>Per Mile</th>
								<th>Minimum</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>Airport Transfer</td>
								<td>$75.00</td>
								<td>$2.50</td>
								<td>$95.00</td>
							</tr>
							<tr>
								<td>Hourly Service (Sedan)</td>
								<td>$65.00/hr</td>
								<td>Included</td>
								<td>2 hours</td>
							</tr>
							<tr>
								<td>Hourly Service (SUV)</td>
								<td>$85.00/hr</td>
								<td>Included</td>
								<td>2 hours</td>
							</tr>
							<tr>
								<td>Event Transportation</td>
								<td>$75.00/hr</td>
								<td>Included</td>
								<td>3 hours</td>
							</tr>
							<tr>
								<td>Corporate Transportation</td>
								<td>$70.00/hr</td>
								<td>Included</td>
								<td>4 hours</td>
							</tr>
						</tbody>
					</table>
				</div>

				<h2>Additional Fees</h2>
				<ul>
					<li><strong>Wait Time:</strong> $15.00 per 15 minutes after first 15 minutes</li>
					<li><strong>After Hours Service (10pm-5am):</strong> 15% surcharge</li>
					<li><strong>Holiday Surcharge:</strong> 20% additional fee</li>
					<li><strong>Additional Stops:</strong> $15.00 per stop</li>
					<li><strong>Cleaning Fee:</strong> $100.00 (if applicable)</li>
				</ul>

				<h2>Payment Methods</h2>
				<p>
					We accept all major credit cards, corporate accounts, and cash payments. A 20% gratuity is
					automatically added to all fares for your convenience. All prices are subject to change
					based on fuel surcharges, local regulations, and market conditions.
				</p>

				<h2>Cancelation Policy</h2>
				<p>
					Cancellations made more than 24 hours before scheduled service will receive a full refund.
					Cancellations made within 24 hours of scheduled service will be subject to a 50%
					cancellation fee. No-shows will be charged the full fare amount.
				</p>

				<div class="alert alert-info mt-8">
					<Accessibility class="h-6 w-6 shrink-0 stroke-current" />
					<div>
						<h3 class="font-bold">Wheelchair Accessible Vehicles</h3>
						<p>Wheelchair accessible vehicles are available upon request at standard rates.</p>
					</div>
				</div>

				<div class="mt-8">
					<a href="https://book.mylimobiz.com/v4/arise" class="btn btn-primary no-underline">Book Now</a>
					<a href="/contact" class="btn btn-outline ml-4 no-underline">Contact Us</a>
				</div>
			</Prose>
		{/snippet}
	</ResponsiveContainer>
</main>
