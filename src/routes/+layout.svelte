<script lang="ts">
	import '../app.css';
	import Header from '$lib/components/Header.svelte';
	import Navigation from '$lib/components/Navigation.svelte'; // Added import
	import type { LayoutData } from './$types';
	import { navItems } from '$lib/navItems'; // Import static navItems directly
	// (Removed viewport import and setContext: backward compatibility no longer needed)

	import { page } from '$app/state';
	import { MetaTags, deepMerge } from 'svelte-meta-tags'; // Import MetaTags and deepMerge
	import posthog from 'posthog-js';
	import { browser } from '$app/environment';
	import { onMount } from 'svelte';
	import { PUBLIC_POSTHOG_KEY } from '$env/static/public'; // Import the public env var
	import '../routes/window'; // Import window type declarations

	let { children, data }: { children: () => any; data: LayoutData } = $props();

	// Calculate merged meta tags using deepMerge
	// Assumes 'data' prop contains { baseMetaTags: { ... } } from the layout load function
	// Assumes page-specific load functions will return { pageMetaTags: { ... } } in $page.data
	let metaTags = $derived(deepMerge(data.baseMetaTags, page.data.pageMetaTags || {}));
	
	// Footer classes are now directly applied with Tailwind responsive modifiers

	onMount(() => {
		if (browser && PUBLIC_POSTHOG_KEY) {
			// Check if in browser and key exists
			posthog.init(
				PUBLIC_POSTHOG_KEY, // Use the environment variable
				{
					api_host: 'https://us.i.posthog.com',
					person_profiles: 'identified_only', // As per instructions
					// You can add more configuration options here if needed
				}
			);
			console.log('PostHog initialized'); // Optional: Add a console log for confirmation
		} else if (browser && !PUBLIC_POSTHOG_KEY) {
			console.warn('PostHog Key not found. Analytics disabled.'); // Warn if key is missing
		}
	});
/* 	if (browser) {
		window.dataLayer = window.dataLayer || [];
		window.gtag = function gtag(){window.dataLayer.push(arguments);};
		window.gtag('js', new Date());
		window.gtag('config', 'G-H66RC4CM9N');
	} */
</script>

<!-- svelte-ignore a11y_missing_attribute -->
<svelte:boundary
	onerror={(error, reset) => {
		console.error('Layout error:', error);
		return {
			message: 'Something went wrong loading the page. Please try again.',
			reset
		};
	}}
>
	<div class="app flex min-h-screen flex-col overflow-x-hidden">
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MKDQB9ZS"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
		
		<MetaTags {...metaTags} />
		<a href="#main-content" class="link link-primary sr-only focus:not-sr-only focus:p-4">
			Skip to main content
		</a>

		<div class="z-50">
			<Header
				logoText="Arise Transit"
				logoHref="/"
				actionHref="https://book.mylimobiz.com/v4/arise"
				actionText="Book Now"
				phoneNumber="tel:+19453436785"
			>
				{#snippet navigation()}
					<Navigation items={navItems} />
					<!-- Removed mode and onClose props -->
					<!-- onClose functionality is now handled within Header via context/direct calls -->
				{/snippet}
			</Header>
		</div>

		<main id="main-content" tabindex="-1" class="relative min-h-[50vh]">
			<div class="relative">
				{@render children()}
			</div>
		</main>

		<footer class="footer footer-center bg-base-200 text-base-content mt-auto py-4 md:py-8">
			<div class="max-w-screen-xl px-4">
				<p>&copy; {new Date().getFullYear()} Arise Transit. All rights reserved.</p>

				<!-- Footer links with responsive layout using Tailwind -->
				<div class="mt-2 flex flex-col items-center gap-2 md:flex-row md:justify-center md:gap-4">
					<!-- Mobile order: Contact, Policy -->
					<!-- Desktop order: Policy, Pricing, Contact -->
					<a href="/policy" class="link link-hover hidden md:inline-block">Our Policy</a>
					<a href="/pricing" class="link link-hover hidden md:inline-block">Pricing</a>
					<a href="/contact" class="link link-hover">Contact Us</a>
					<a href="/policy" class="link link-hover md:hidden">Our Policy</a>
					<!-- Shown only on mobile -->
				</div>
			</div>
		</footer>
	</div>
</svelte:boundary>
<!-- <svelte:head>
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-H66RC4CM9N">
	</script>
</svelte:head> -->
