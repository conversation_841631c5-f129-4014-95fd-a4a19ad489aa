import { type RequestHandler } from '@sveltejs/kit';
import { ImageResponse } from '@vercel/og';
import { error } from '@sveltejs/kit';

// Arise Transit brand colors
const BRAND_COLORS = {
	primary: '#1B4E3F', // Deep green
	secondary: '#F4A261', // Warm orange
	accent: '#2A9D8F', // Teal
	background: '#FFFFFF',
	text: '#2B2B2B',
	muted: '#6B7280'
};

export const GET: RequestHandler = async ({ url }) => {
	try {
		// Parse URL parameters with validation
		const searchParams = url.searchParams;
		const title = searchParams.get('title') || 'Arise Transit - Premium Transportation Services';
		const description = searchParams.get('description') || 'Luxury SUV transportation services in Dallas. Professional chauffeurs, premium vehicles, and exceptional service for airport transfers, corporate travel, and special events.';
		const type = (searchParams.get('type') as 'default' | 'service' | 'blog' | 'page') || 'default';

		// Validate parameters
		if (title.length > 120) {
			throw error(400, 'Title must be 120 characters or less');
		}
		if (description.length > 300) {
			throw error(400, 'Description must be 300 characters or less');
		}

		// Dynamic sizing based on type
		const titleSize = type === 'service' ? 52 : 48;
		const descSize = type === 'service' ? 26 : 24;

		// Create React-element-like object (Satori non-JSX approach)
		const ogElement = {
			type: 'div',
			props: {
				style: {
					height: '630px',
					width: '1200px',
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'center',
					justifyContent: 'center',
					background: `linear-gradient(135deg, ${BRAND_COLORS.primary} 0%, ${BRAND_COLORS.accent} 100%)`,
					padding: '60px',
					fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
					color: 'white',
					textAlign: 'center',
					boxSizing: 'border-box',
					position: 'relative'
				},
				children: [
					// Logo and Brand Section
					{
						type: 'div',
						props: {
							style: {
								display: 'flex',
								alignItems: 'center',
								marginBottom: '40px'
							},
							children: [
								{
									type: 'div',
									props: {
										style: {
											width: '80px',
											height: '80px',
											background: 'white',
											borderRadius: '50%',
											display: 'flex',
											alignItems: 'center',
											justifyContent: 'center',
											marginRight: '20px'
										},
										children: {
											type: 'div',
											props: {
												style: {
													width: '60px',
													height: '60px',
													background: BRAND_COLORS.primary,
													borderRadius: '50%',
													display: 'flex',
													alignItems: 'center',
													justifyContent: 'center',
													fontWeight: 'bold',
													color: 'white',
													fontSize: '24px'
												},
												children: 'AT'
											}
										}
									}
								},
								{
									type: 'div',
									props: {
										style: {
											fontSize: '32px',
											fontWeight: '700',
											color: 'white'
										},
										children: 'ARISE TRANSIT'
									}
								}
							]
						}
					},
					// Content Section
					{
						type: 'div',
						props: {
							style: {
								maxWidth: '900px'
							},
							children: [
								{
									type: 'h1',
									props: {
										style: {
											fontSize: `${titleSize}px`,
											fontWeight: '700',
											margin: '0 0 20px 0',
											lineHeight: '1.2',
											color: 'white'
										},
										children: title
									}
								},
								{
									type: 'p',
									props: {
										style: {
											fontSize: `${descSize}px`,
											fontWeight: '400',
											margin: '0',
											lineHeight: '1.4',
											color: 'rgba(255, 255, 255, 0.9)'
										},
										children: description
									}
								}
							]
						}
					},
					// Badge Section
					{
						type: 'div',
						props: {
							style: {
								marginTop: '40px',
								background: 'rgba(255, 255, 255, 0.1)',
								padding: '12px 24px',
								borderRadius: '25px',
								fontSize: '16px',
								fontWeight: '500',
								color: 'white'
							},
							children: 'Premium Transportation Services'
						}
					},
					// Pattern Overlay
					{
						type: 'div',
						props: {
							style: {
								position: 'absolute',
								top: '0',
								left: '0',
								right: '0',
								bottom: '0',
								background: 'radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255,255,255,0.05) 0%, transparent 50%)',
								pointerEvents: 'none'
							}
						}
					}
				]
			}
		};

		return new ImageResponse(
			ogElement,
			{
				width: 1200,
				height: 630
			}
		);

	} catch (err) {
		console.error('OG Image generation error:', err);

		// Return a simple fallback image using React-element-like object
		const fallbackElement = {
			type: 'div',
			props: {
				style: {
					height: '630px',
					width: '1200px',
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'center',
					justifyContent: 'center',
					background: BRAND_COLORS.primary,
					fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
					color: 'white',
					textAlign: 'center'
				},
				children: [
					{
						type: 'h1',
						props: {
							style: {
								fontSize: '48px',
								fontWeight: '700',
								margin: '0 0 20px 0'
							},
							children: 'Arise Transit'
						}
					},
					{
						type: 'p',
						props: {
							style: {
								fontSize: '24px',
								margin: '0'
							},
							children: 'Premium Transportation Services'
						}
					}
				]
			}
		};

		return new ImageResponse(
			fallbackElement,
			{
				width: 1200,
				height: 630
			}
		);
	}
};
