/** @jsxImportSource react */
import { type RequestHandler } from '@sveltejs/kit';
import { ImageResponse } from '@vercel/og';
import { error } from '@sveltejs/kit';

// Arise Transit brand colors
const BRAND_COLORS = {
	primary: '#1B4E3F', // Deep green
	secondary: '#F4A261', // Warm orange
	accent: '#2A9D8F', // Teal
	background: '#FFFFFF',
	text: '#2B2B2B',
	muted: '#6B7280'
};

export const GET: RequestHandler = async ({ url }) => {
	try {
		// Parse URL parameters with validation
		const searchParams = url.searchParams;
		const title = searchParams.get('title') || 'Arise Transit - Premium Transportation Services';
		const description = searchParams.get('description') || 'Luxury SUV transportation services in Dallas. Professional chauffeurs, premium vehicles, and exceptional service for airport transfers, corporate travel, and special events.';
		const type = (searchParams.get('type') as 'default' | 'service' | 'blog' | 'page') || 'default';
		
		// Validate parameters
		if (title.length > 120) {
			throw error(400, 'Title must be 120 characters or less');
		}
		if (description.length > 300) {
			throw error(400, 'Description must be 300 characters or less');
		}
		
		// Dynamic sizing based on type
		const titleSize = type === 'service' ? '52px' : '48px';
		const descSize = type === 'service' ? '26px' : '24px';
		
		// Create HTML string for the OG image
		const htmlContent = `
		<div style="
			height: 630px;
			width: 1200px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, ${BRAND_COLORS.primary} 0%, ${BRAND_COLORS.accent} 100%);
			padding: 60px;
			font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
			color: white;
			text-align: center;
			box-sizing: border-box;
			position: relative;
		">
			<!-- Logo and Brand Section -->
			<div style="
				display: flex;
				align-items: center;
				margin-bottom: 40px;
			">
				<div style="
					width: 80px;
					height: 80px;
					background: white;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20px;
				">
					<div style="
						width: 60px;
						height: 60px;
						background: ${BRAND_COLORS.primary};
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						font-weight: bold;
						color: white;
						font-size: 24px;
					">AT</div>
				</div>
				<div style="
					font-size: 32px;
					font-weight: 700;
					color: white;
				">ARISE TRANSIT</div>
			</div>
			
			<!-- Content Section -->
			<div style="max-width: 900px;">
				<h1 style="
					font-size: ${titleSize};
					font-weight: 700;
					margin: 0 0 20px 0;
					line-height: 1.2;
					color: white;
				">${title}</h1>
				
				<p style="
					font-size: ${descSize};
					font-weight: 400;
					margin: 0;
					line-height: 1.4;
					color: rgba(255, 255, 255, 0.9);
				">${description}</p>
			</div>
			
			<!-- Badge Section -->
			<div style="
				margin-top: 40px;
				background: rgba(255, 255, 255, 0.1);
				padding: 12px 24px;
				border-radius: 25px;
				font-size: 16px;
				font-weight: 500;
				color: white;
			">Premium Transportation Services</div>
			
			<!-- Pattern Overlay -->
			<div style="
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255,255,255,0.05) 0%, transparent 50%);
				pointer-events: none;
			"></div>
		</div>
		`.trim();
		
		return new ImageResponse(
			htmlContent,
			{
				width: 1200,
				height: 630
			}
		);
		
	} catch (err) {
		console.error('OG Image generation error:', err);
		
		// Return a simple fallback image
		const fallbackHtml = `
		<div style="
			height: 630px;
			width: 1200px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: ${BRAND_COLORS.primary};
			font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
			color: white;
			text-align: center;
		">
			<h1 style="
				font-size: 48px;
				font-weight: 700;
				margin: 0 0 20px 0;
			">Arise Transit</h1>
			<p style="
				font-size: 24px;
				margin: 0;
			">Premium Transportation Services</p>
		</div>
		`.trim();
		
		return new ImageResponse(
			fallbackHtml,
			{
				width: 1200,
				height: 630
			}
		);
	}
};
