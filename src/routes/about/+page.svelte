<script lang="ts">
	import Prose from '$lib/components/Prose.svelte';
	import ResponsiveContainer from '$lib/components/ResponsiveContainer.svelte';
	import Hero from '$lib/components/Hero.svelte';
	import { Award, Clock, Shield } from 'lucide-svelte';
	import { goto } from '$app/navigation';

	const navigateTo = (path: string) => {
		goto(path);
	};
</script>

<svelte:head>
	<title>About Us - Arise Transit</title>
	<meta
		name="description"
		content="Learn about Arise Transit's commitment to exceptional transportation services and our company mission."
	/>
</svelte:head>

<div class="mb-8">
	<Hero
		variant="overlay"
		minHeight="min-h-[40vh]"
		contentWidth="max-w-4xl"
		contentLayout="centered"
		textSize="large"
		bgClass="bg-neutral"
		backgroundEffect="rainbow-stripes"
	>
		{#snippet heading()}
			<h1 class="mb-4 text-4xl font-bold md:text-5xl text-neutral">About Arise Transit</h1>
		{/snippet}

		{#snippet content()}
			<p class="text-xl">Exceptional transportation services tailored to your needs</p>
		{/snippet}

		{#snippet cta()}
			<span>Our Story</span>
		{/snippet}
	</Hero>
</div>

<main class="py-8">
	<ResponsiveContainer class="mx-auto max-w-4xl">
		{#snippet children()}
			<Prose as="article" size="xl">
				<p id="our-story" class="lead">
					At Arise Transit, we are committed to delivering safe, reliable, and comfortable
					transportation solutions. Our mission is to exceed customer expectations by providing
					punctual, professional service with a focus on personalized care.
				</p>

				<div class="my-8 grid grid-cols-1 gap-6 md:grid-cols-3">
					<div class="card bg-base-100 shadow-lg transition-shadow hover:shadow-xl">
						<div class="card-body">
							<Award class="text-primary mb-2 h-10 w-10" />
							<h3 class="card-title">Excellence</h3>
							<p>
								We maintain the highest standards in service quality, vehicle maintenance, and
								driver training.
							</p>
						</div>
					</div>
					<div class="card bg-base-100 shadow-lg transition-shadow hover:shadow-xl">
						<div class="card-body">
							<Clock class="text-primary mb-2 h-10 w-10" />
							<h3 class="card-title">Reliability</h3>
							<p>
								Punctual service with real-time flight tracking and 24/7 support for all your
								transportation needs.
							</p>
						</div>
					</div>
					<div class="card bg-base-100 shadow-lg transition-shadow hover:shadow-xl">
						<div class="card-body">
							<Shield class="text-primary mb-2 h-10 w-10" />
							<h3 class="card-title">Safety</h3>
							<p>
								Your safety is our priority with rigorously maintained vehicles and professionally
								trained drivers.
							</p>
						</div>
					</div>
				</div>

				<h2>Why Choose Us?</h2>
				<ul>
					<li>Professional and courteous drivers</li>
					<li>Modern, well-maintained fleet</li>
					<li>Competitive pricing with no hidden fees</li>
					<li>24/7 customer support</li>
					<li>Customized solutions for individual and corporate clients</li>
				</ul>

				<h2>Our Services</h2>
				<p>We offer a comprehensive range of transportation services including:</p>

				<h3>Airport Transfers</h3>
				<p>
					Reliable and punctual airport pickup and drop-off services with flight tracking to
					accommodate any schedule changes.
				</p>

				<h3>Corporate Transportation</h3>
				<p>
					Impress your clients and provide convenient transportation for your employees with our
					corporate services. We can handle events of any size.
				</p>

				<blockquote>
					"Arise Transit provided exceptional service for our corporate event. The drivers were
					professional, the vehicles were immaculate, and the coordination was flawless."
					<cite>— Sarah Johnson, Event Coordinator</cite>
				</blockquote>

				<h3>Special Events</h3>
				<p>
					Make your special day even more memorable with our luxury transportation services for
					weddings, proms, and other important occasions.
				</p>

				<div class="mt-8 flex flex-wrap gap-4">
					<a href="/pricing" class="btn btn-primary no-underline">View Pricing</a>
					<a href="/contact" class="btn btn-outline no-underline">Contact Us</a>
				</div>
			</Prose>
		{/snippet}
	</ResponsiveContainer>
</main>
