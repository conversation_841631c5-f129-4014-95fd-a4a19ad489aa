import { superValidate, message as setMessage } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { fail, redirect, isRedirect } from '@sveltejs/kit';
import { getDb } from '$lib/server/db'; // Import the database function
import { contacts } from '$lib/server/db/schema';
import { schema } from './schema';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;
export const load = (async () => {
  // Initialize an empty form
  const form = await superValidate(zod(schema));
  return { form };
}) satisfies PageServerLoad;

export const actions: Actions = {
  default: async ({ request }) => { // Removed locals as it's not needed here anymore
    // Validate the form with SuperForms
    const form = await superValidate(request, zod(schema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      let db;
      try {
        // Get the DB instance by calling getDb
        db = await getDb();
        if (!db) {
          // This case should ideally not happen if init succeeded, but good practice to check
          throw new Error('Database connection is not available.');
        }

        // Proceed with insertion using the obtained db instance
        await db.insert(contacts).values({
          name: form.data.name,
          email: form.data.email,
          phone: form.data.phone || null,
          message: form.data.message
        });
      } catch (dbError) {
        // Log the database error (could be connection error from getDb or insert error)
        console.error('Database operation failed:', dbError);

        // Log the submission data as a fallback
        console.log('Form submission data (database failed):', {
          name: form.data.name,
          email: form.data.email,
          message: form.data.message
        });

        // Return a user-friendly error message
        return setMessage(form, 'Your message could not be saved due to a database issue. Please try again later or contact support.', { status: 500 });
      }

      // Redirect to thank you page on successful database insertion
      throw redirect(303, '/thank-you'); // 303 See Other is appropriate after POST
    } catch (error) {
      // Use isRedirect to check if it's a SvelteKit redirect
      if (isRedirect(error)) { // Use the helper function
        throw error; // Re-throw the redirect for SvelteKit to handle
      }
      // Otherwise, it's an unexpected error during processing
      console.error('Error processing contact form:', error);
      // Return error message with the form
      return setMessage(form, 'Could not submit form due to an unexpected error. Please try again later.', { status: 500 });
    }
  },
};