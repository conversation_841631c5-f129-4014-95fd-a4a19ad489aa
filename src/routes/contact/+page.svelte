<script lang="ts">
	import Prose from '$lib/components/Prose.svelte';
	import ResponsiveContainer from '$lib/components/ResponsiveContainer.svelte';
	import Hero from '$lib/components/Hero.svelte';
	import { Mail, Phone, Clock, Send } from 'lucide-svelte';
	// Import FormSnap components along with superforms
	import { isDesktop } from '$lib/utils/viewport.svelte';
	import { superForm } from 'sveltekit-superforms/client';
	import { Field, Control, Label, Description, FieldErrors } from 'formsnap';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { schema } from './schema';
	import type { PageData } from './$types';

	// Use viewport utility consistently as per CLAUDE.md guidelines
	const isDesktopView = $derived($isDesktop);

	// Access form data from PageData
	let { data } = $props();

	// Initialize the form with SuperForms
	const form = superForm(data.form, {
		multipleSubmits: 'prevent',
		validators: zodClient(schema),
		// Use SvelteKit's navigation
		taintedMessage: false,
		// Allow SvelteKit's default navigation/redirect handling
	});
	const { form: formData, errors, constraints, enhance, message, submitting } = form;
</script>

<svelte:head>
	<title>Contact Us - Arise Transit</title>
	<meta
		name="description"
		content="Get in touch with Arise Transit for transportation services, quotes, or assistance."
	/>
</svelte:head>

<div class="mb-8">
	<Hero
		variant="overlay"
		minHeight="min-h-[40vh]"
		contentWidth="max-w-4xl"
		contentLayout="centered"
		textSize="large"
		bgClass="bg-neutral"
		backgroundEffect="rainbow-stripes"
	>
		{#snippet heading()}
			<h1 class="mb-4 text-4xl font-bold md:text-5xl text-neutral">Contact Us</h1>
		{/snippet}

		{#snippet content()}
			<p class="text-xl">We're here to assist with all your transportation needs</p>
		{/snippet}

		{#snippet cta()}
			<span>Get in Touch</span>
		{/snippet}
	</Hero>
</div>

<main class="py-8">
	<ResponsiveContainer>
		{#snippet children()}
			<div class="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2">
				<!-- Contact Information Section -->
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<h2 class="card-title text-2xl">Contact Information</h2>
						<p class="mb-4">
							Our team is available 24/7 to assist with bookings, inquiries, and support.
						</p>

						<div class="join join-vertical w-full">
							<!-- Phone Information -->
							<div class="collapse-arrow join-item border-base-300 collapse border">
								<input type="radio" name="contact-accordion" checked />
								<div class="collapse-title flex items-center gap-2 text-lg font-medium">
									<Phone class="text-primary h-5 w-5" /> Phone
								</div>
								<div class="collapse-content">
									<p>
										<a href="tel:+19453436785" class="link link-primary">(*************</a> (Main
										Office)<br />
										<a href="tel:+19453436785" class="link link-primary">(*************</a> (Dispatch
										- 24/7)
									</p>
								</div>
							</div>

							<!-- Email Information -->
							<div class="collapse-arrow join-item border-base-300 collapse border">
								<input type="radio" name="contact-accordion" />
								<div class="collapse-title flex items-center gap-2 text-lg font-medium">
									<Mail class="text-primary h-5 w-5" /> Email
								</div>
								<div class="collapse-content">
									<p>
										<a href="mailto:<EMAIL>" class="link link-primary"
											><EMAIL></a
										>
										(General Inquiries)<br />
										<a href="mailto:<EMAIL>" class="link link-primary"
											><EMAIL></a
										>
										(Reservations)<br />
										<a href="mailto:<EMAIL>" class="link link-primary"
											><EMAIL></a
										> (Customer Support)
									</p>
								</div>
							</div>

							<!-- Hours Information -->
							<div class="collapse-arrow join-item border-base-300 collapse border">
								<input type="radio" name="contact-accordion" />
								<div class="collapse-title flex items-center gap-2 text-lg font-medium">
									<Clock class="text-primary h-5 w-5" /> Hours of Operation
								</div>
								<div class="collapse-content">
									<p>
										<strong>Office:</strong> Monday-Friday, 8:00 AM - 6:00 PM<br />
										<strong>Dispatch:</strong> 24 hours, 7 days a week
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Contact Form Section -->
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<h2 class="card-title text-2xl">Send Us a Message</h2>

						<!-- Success/Error Messages -->
						{#if $message}
							<!-- Show success message -->
							<div class="alert alert-success mb-6">
								<div>
									<h3 class="font-bold">Message sent!</h3>
									<p>{$message}</p>
								</div>
							</div>

							<!-- Replace form with thank you message -->
							<div class="text-center p-8 mb-4 bg-base-200 rounded-lg">
								<h3 class="text-xl font-semibold mb-3">Thank you for reaching out!</h3>
								<p class="mb-4">We've received your message and will get back to you as soon as possible.</p>
								<button class="btn btn-primary" onclick={() => window.location.reload()}>
									Send another message
								</button>
							</div>
						{:else}
							<!-- FormSnap Form - Only show if no success message -->
							<form method="POST" use:enhance class="space-y-4">
							<!-- Name Input -->
							<Field {form} name="name">
								<Control>
									{#snippet children({ props })}
										<Label for="name" class="label">
											<span class="label-text font-medium">Name</span>
										</Label>
										<input
											{...props}
											type="text"
											id="name"
											class="input input-bordered w-full"
											class:input-error={$errors.name}
											bind:value={$formData.name}
											placeholder="Your name"
										/>
									{/snippet}
								</Control>
								<FieldErrors class="text-error text-xs mt-1" />
							</Field>

							<!-- Email Input -->
							<Field {form} name="email">
								<Control>
									{#snippet children({ props })}
										<Label for="email" class="label">
											<span class="label-text font-medium">Email</span>
										</Label>
										<input
											{...props}
											type="email"
											id="email"
											class="input input-bordered w-full"
											class:input-error={$errors.email}
											bind:value={$formData.email}
											placeholder="Your email address"
										/>
									{/snippet}
								</Control>
								<FieldErrors class="text-error text-xs mt-1" />
							</Field>

							<!-- Phone Input -->
							<Field {form} name="phone">
								<Control>
									{#snippet children({ props })}
										<Label for="phone" class="label">
											<span class="label-text font-medium">Phone (optional)</span>
										</Label>
										<input
											{...props}
											type="tel"
											id="phone"
											class="input input-bordered w-full"
											class:input-error={$errors.phone}
											bind:value={$formData.phone}
											placeholder="Your phone number"
										/>
									{/snippet}
								</Control>
								<Description class="text-xs text-gray-500 mt-1">Optional, but helpful for urgent requests</Description>
								<FieldErrors class="text-error text-xs mt-1" />
							</Field>

							<!-- Message Textarea -->
							<Field {form} name="message">
								<Control>
									{#snippet children({ props })}
										<Label for="message" class="label">
											<span class="label-text font-medium">Message</span>
										</Label>
										<textarea
											{...props}
											id="message"
											class="textarea textarea-bordered h-32 w-full"
											class:textarea-error={$errors.message}
											bind:value={$formData.message}
											placeholder="How can we help you?"
										></textarea>
									{/snippet}
								</Control>
								<FieldErrors class="text-error text-xs mt-1" />
							</Field>

							<!-- Submit Button -->
							<div class="form-control mt-6">
								<button type="submit" class="btn btn-primary" disabled={$submitting}>
									<Send class="mr-2 h-5 w-5" /> {$submitting ? 'Sending...' : 'Send Message'}
								</button>
							</div>
						</form>
						{/if}

						<div class="mt-6 text-sm">
							<p>
								For immediate assistance, please call our 24/7 dispatch at
								<a href="tel:+19453436785" class="link link-primary">(*************</a>.
							</p>
						</div>
					</div>
				</div>
			</div>
		{/snippet}
	</ResponsiveContainer>
</main>