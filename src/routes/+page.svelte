<script lang="ts">
	import { Hero, ServicesSection, CTABanner, ServiceFitSection, BenefitsSection } from '$lib';
	import type { Picture as _Picture } from 'vite-imagetools';
	// import { goto } from '$app/navigation';
	import { servicesData } from '$lib/data/services'; // Import static data directly
	import suvHeroImage from '$lib/assets/images/suv-hero.jpg?enhanced';
	// Viewport utilities removed – use Tailwind responsive classes instead.
	import { CalendarDays } from 'lucide-svelte';
</script>

<div class="flex min-h-screen flex-col">
	<!-- Hero Section with Named Snippets -->
	<Hero
		variant="full"
		contentWidth="w-full"
		minHeight="min-h-[100dvh] md:h-screen"
		image={{
			sources: suvHeroImage.sources,
			img: suvHeroImage.img
		}}
		contentLayout="split"
		textSize="large"
		ctaStyle="prominent"
		ctaPosition="right"
		ctaHref="https://book.mylimobiz.com/v4/arise"
	>
		{#snippet heading()}
			<h1>
				Stress-Free
				<br class="block md:hidden" />
				Luxury Travel
				<br class="block md:hidden" />
				On Your Schedule
			</h1>
		{/snippet}

		{#snippet content()}
			Premium vehicles, professional drivers, and seamless service - all at your fingertips
		{/snippet}

		{#snippet cta()}
			<span class="flex items-center gap-2">
				<span>
					<CalendarDays class="text-neutral-content"size={20} />
				</span>
				Book Now
			</span>
		{/snippet}
	</Hero>

	<!-- Content Sections -->
	<div class="relative">
		<!-- Benefits Section -->
		<BenefitsSection />
		<!-- Services Section -->
		<ServicesSection sectionTitle="Our Premium Services" cardItems={servicesData} />

		<!-- Service Fit Section -->
		<ServiceFitSection sectionTitle="Perfect For" backgroundClass="bg-base-300" />

		<!-- CTA Banner -->
		<CTABanner
			variant="image"
			content={{
				mainMessage: 'Ready to Experience Luxury Travel?',
				subMessage: 'Book your premium transportation service today',
				buttonText: 'Book Now',
				buttonHref: 'https://book.mylimobiz.com/v4/arise'
			}}
			background={{ style: 'background-color: rgba(0,0,0,0.8);' }}
		/>
	</div>
</div>
