<script lang="ts">
	// Icon for the alert
	import <PERSON> from '$lib/components/Hero.svelte';
</script>

<svelte:head>
	<title>Thank You - Arise Transit</title>
	<meta
		name="description"
		content="Thank you for contacting Arise Transit. We have received your message and will respond shortly."
	/>
</svelte:head>

<!-- Wrapper div for top padding -->


<!-- Wrapper div for top padding -->
<div class="pt-16">

<!-- 1. Reinstated Hero Component -->
<Hero variant="centered" bgClass="bg-base-200" minHeight="min-h-[30vh]">
	<div class="max-w-lg">
		<h1 class="text-5xl font-bold mb-6">Thank You for Reaching Out!</h1>
		<div role="alert" class="alert alert-success shadow-lg">
			<!-- SVG Checkmark Icon -->
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="stroke-current h-6 w-6 shrink-0"
				fill="none"
				viewBox="0 0 24 24"
				><path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
				/></svg
			>
			<span>
				Your message has been successfully received. We appreciate you contacting Arise Transit. We
				aim to reply within <span class="badge badge-outline">24 hours</span>.
			</span>
		</div>
	</div>
</Hero>
</div> <!-- Close wrapper div -->
<!-- 2. "What's Next?" Card Section -->
<div class="flex justify-center my-12"> <!-- Adjusted margin -->
	<div class="card w-full max-w-lg bg-base-100 shadow-xl">
		<div class="card-body items-center text-center">
			<h2 class="card-title">What's Next?</h2>
			<p>While you wait for our response, you might find these helpful:</p>
			<div class="card-actions justify-center mt-4">
				<a href="/faq" class="btn btn-primary">Explore our FAQ</a>
				<a href="/contact" class="btn btn-neutral ml-4">Make Another Request</a>
			</div>
			
		
		</div>
	</div>
</div>

