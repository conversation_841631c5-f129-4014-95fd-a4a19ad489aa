<script>
	import { page } from '$app/stores';
	import { captureError, addBreadcrumb } from '$lib/utils/sentry';
	
	// Fun error messages for 404 pages
	const notFoundMessages = [
		'Oops! This page took a wrong turn.',
		"Hmm, we can't find this destination on our map.",
		"Looks like this ride doesn't exist yet!",
		'This page seems to have missed its stop.',
		"This route isn't in our transit system yet."
	];
	
	// Fun error messages for other errors
	const errorMessages = [
		'Well, this is awkward... something went wrong.',
		'Our transit system hit a speed bump.',
		'Looks like we hit a roadblock.',
		'Houston, we have a problem.',
		'Our digital chauffeur needs a moment.'
	];
	
	// Select a random message based on error type
	const randomMessage =
		$page.status === 404
			? notFoundMessages[Math.floor(Math.random() * notFoundMessages.length)]
			: errorMessages[Math.floor(Math.random() * errorMessages.length)];
	
	// Handle the report issue button click
	function handleReportIssue() {
		// Add a breadcrumb to track that user manually reported this
		addBreadcrumb(
			'User manually reported an error',
			'user.report',
			'warning',
			{ errorPage: $page.status.toString() }
		);
		
		// Capture the error with additional context
		captureError(
			new Error($page.error?.message || 'User-reported error'),
			{ 
				userReported: true,
				url: window.location.href,
				status: $page.status
			}
		);
		
		alert('Thank you for reporting this issue. Our team will look into it.');
	}
</script>

<svelte:head>
	<title>{$page.status} | {$page.error?.message || 'Error'}</title>
</svelte:head>

<div class="bg-base-100 flex min-h-screen flex-col items-center justify-center px-4 py-12">
	<div
		class="bg-base-200 w-full max-w-xl rounded-lg text-center shadow-xl p-6 md:p-10"
	>
		<div class="mb-6 flex flex-col items-center">
			<!-- Fun emoji based on error type -->
			<span class="mb-4 text-6xl">
				{#if $page.status === 404}
					🚏
				{:else if $page.status === 500}
					🚨
				{:else}
					🚦
				{/if}
			</span>

			<h1 class="text-4xl font-bold sm:text-5xl">{$page.status}</h1>
		</div>

		<!-- Fun, randomized error message -->
		<p class="mb-6 text-xl">{randomMessage}</p>

		{#if $page.status === 404}
			<p class="text-base-content/70 mb-8">
				Don't worry, our other routes are still running smoothly.
			</p>
		{:else}
			<p class="text-base-content/70 mb-8">
				Our team has been notified and is working to get things back on track.
			</p>
		{/if}

		<div class="flex flex-col md:flex-row justify-center gap-4">
			<a href="/" class="btn btn-primary">Back to Home</a>

			{#if $page.status !== 404}
				<button class="btn btn-outline" onclick={handleReportIssue}>
					Report Issue
				</button>
			{/if}
		</div>
	</div>
</div>