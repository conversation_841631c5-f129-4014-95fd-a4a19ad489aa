import type { LayoutServerLoad } from './$types';
import { getPostHogClient } from '$lib/server/posthog';
import { building } from '$app/environment'; // Import the building flag

export const prerender = true;
export const load: LayoutServerLoad = async (event) => {
	const { locals, request, url } = event; // Destructure event
	// Common data loading for layout
	try {
		const baseTitle = 'Arise Transit';
		const baseMetaTags = {
			title: `${baseTitle} - Your Premier Transportation Service`,
			description:
				'Arise Transit provides luxury transportation services in Dallas for airport transfers, corporate events, and special occasions.',
			url: url.href, // Use destructured url directly
			ogImage: '/placeholder-og-image.png' // Placeholder OG image
		};

		// PostHog Server-Side Tracking
		const posthogClient = getPostHogClient();
		if (posthogClient) {
			// TODO: Replace this placeholder with the actual user ID from your session/auth (e.g., event.locals.user?.id)
			// If the user is not logged in, you might use a session ID or another unique identifier.
			// Only get clientAddress if not building/prerendering
			const clientAddress = building ? 'prerendering' : event.getClientAddress();
			const distinctId = event.locals.user?.id ?? `anon_server_${clientAddress}`;

			posthogClient.capture({
				distinctId: distinctId,
				event: 'Layout Loaded (Server)',
				properties: {
					// Add any relevant properties about the request or user
					$current_url: event.url.pathname,
					userAgent: event.request.headers.get('user-agent'),
				}
			});

			// IMPORTANT: Always shutdown the client after capturing events server-side
			await posthogClient.shutdown();
		}

		return {
			user: locals.user, // Example common data
			// navigation: navItems, // Removed: navItems are static, import directly in +layout.svelte
			baseMetaTags,
			// Pass viewport width hint from Client Hints header if available
			viewportWidthHint: request.headers.get('sec-ch-viewport-width') !== null && request.headers.get('sec-ch-viewport-width') !== undefined ? Number(request.headers.get('sec-ch-viewport-width')) : undefined
		};
	} catch (error) {
		// Error handling
		console.error('Layout load error:', error);
		return {
			user: null,
			// navigation: [], // Removed: navItems are static
			baseMetaTags: {
				title: 'Arise Transit',
				description: 'Luxury Black Car Service in Dallas, TX',
				url: url.href, // Use destructured url directly
				ogImage: '/placeholder-og-image.png' // Fallback OG image
			},
			viewportWidthHint: undefined // Fallback if error occurs
		};
	}
};
