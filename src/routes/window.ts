// eslint-disable-next-line @typescript-eslint/no-unused-vars
declare interface Window {
    dataLayer: IArguments[],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    gtag?: (...args: any[]) => void,
    // iframe-resizer type declarations
    iFrameResize?: (options: {
        log?: boolean;
        inPageLinks?: boolean;
        checkOrigin?: boolean | string[];
        onMessage?: (messageData: { message: string; iframe: HTMLIFrameElement }) => void;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        [key: string]: any;
    }, target: string | HTMLIFrameElement) => Array<{
        close: () => void;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        [key: string]: any;
    }>;
}