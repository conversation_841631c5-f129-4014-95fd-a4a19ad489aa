<script lang="ts">
	import { browser } from '$app/environment';
	import { onMount } from 'svelte';
	import '../../routes/window'; // Import window type declarations

	// Using onMount to ensure the code runs after the component is mounted to the DOM
	onMount(() => {
		// Only run in browser environment
		if (!browser) return;
		
		// Wait for the iframe to be available in the DOM
		setTimeout(() => {
			const iframe = document.getElementById('ores');
			if (iframe && typeof window.iFrameResize === 'function') {
				console.log('Initializing iframe-resizer'); // Log for debugging
				
				// Initialize iframe-resizer directly on the window object
				const resizer = window.iFrameResize(
					{
						log: true, // Enable console logging for debugging purposes
						inPageLinks: true,
						checkOrigin: false,
						onMessage: function(messageData) {
							console.log("Received message from iframe:", messageData.message); // Diagnostic log
							try {
								// Assuming messageData.message is a stringified JSON
								var msg = JSON.parse(messageData.message);
								console.log("Pushing event to dataLayer:", msg);
								window.dataLayer = window.dataLayer || [];
								window.dataLayer.push(msg);
							} catch (e) {
								console.error("Error processing message from iframe:", e);
							}
						},
					},
					"#ores"
				);
				
				// Return cleanup function to properly dispose of the resizer when component unmounts
				return () => {
					if (resizer && resizer.length > 0 && typeof resizer[0].close === 'function') {
						console.log('Cleaning up iframe-resizer');
						resizer[0].close();
					}
				};
			} else {
				console.warn('iframe-resizer not available or iframe not found');
			}
		}, 1000); // Give the iframe some time to load
	});
</script>

<svelte:head>
	<title>Book a Ride</title>
	<!-- Google Tag Manager -->
	<script>
		(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-MKDQB9ZS');
	</script>
	<!-- End Google Tag Manager -->
	<script 
		src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.9/iframeResizer.min.js" 
		integrity="sha512-+bpyZqiNr/4QlUd6YnrAeLXzgooA1HKN5yUagHgPSMACPZgj8bkpCyZezPtDy5XbviRm4w8Z1RhfuWyoWaeCyg==" 
		crossorigin="anonymous" 
		referrerpolicy="no-referrer">
	</script>
</svelte:head>

<div class="mb-8">
	<div class="px-4 pt-24 md:px-6">
		<h1 class="mb-4 text-4xl font-bold md:text-5xl">Book a Ride</h1>
	</div>
</div>

<div class="w-full px-4 md:px-6">
	<iframe id="ores" src="https://book.mylimobiz.com/v4/arise" style="width: 1px; min-width: 100%" title="Booking System"></iframe>
</div>
