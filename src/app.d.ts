// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			// Existing properties
			user: User | null;
			// db property removed
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

// You'll also need to define the User type
interface User {
	// Add relevant user properties here
	id: string;
	email: string;
	// ... other user properties
}

export {};

declare module '*?enhanced' {
	const value: import('@sveltejs/enhanced-img').EnhancedImage;
	export default value;
}
