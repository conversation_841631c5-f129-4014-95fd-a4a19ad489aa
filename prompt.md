LEARN SVELTE5 TRANSITIONS

---

## title: transition:

A _transition_ is triggered by an element entering or leaving the DOM as a result of a state change.

When a block (such as an `{#if ...}` block) is transitioning out, all elements inside it, including those that do not have their own transitions, are kept in the DOM until every transition in the block has been completed.

The `transition:` directive indicates a _bidirectional_ transition, which means it can be smoothly reversed while the transition is in progress.

```svelte
<script>
	+++import { fade } from 'svelte/transition';+++

	let visible = $state(false);
</script>

<button onclick={() => (visible = !visible)}>toggle</button>

{#if visible}
	<div +++transition:fade+++>fades in and out</div>
{/if}
```

## Built-in transitions

A selection of built-in transitions can be imported from the [`svelte/transition`](svelte-transition) module.

## Local vs global

Transitions are local by default. Local transitions only play when the block they belong to is created or destroyed, _not_ when parent blocks are created or destroyed.

```svelte
{#if x}
	{#if y}
		<p transition:fade>fades in and out only when y changes</p>

		<p transition:fade|global>fades in and out when x or y change</p>
	{/if}
{/if}
```

## Transition parameters

Transitions can have parameters.

(The double `{{curlies}}` aren't a special syntax; this is an object literal inside an expression tag.)

```svelte
{#if visible}
	<div transition:fade={{ duration: 2000 }}>fades in and out over two seconds</div>
{/if}
```

## Custom transition functions

```js
/// copy: false
// @noErrors
transition = (node: HTMLElement, params: any, options: { direction: 'in' | 'out' | 'both' }) => {
	delay?: number,
	duration?: number,
	easing?: (t: number) => number,
	css?: (t: number, u: number) => string,
	tick?: (t: number, u: number) => void
}
```

Transitions can use custom functions. If the returned object has a `css` function, Svelte will generate keyframes for a [web animation](https://developer.mozilla.org/en-US/docs/Web/API/Web_Animations_API).

The `t` argument passed to `css` is a value between `0` and `1` after the `easing` function has been applied. _In_ transitions run from `0` to `1`, _out_ transitions run from `1` to `0` — in other words, `1` is the element's natural state, as though no transition had been applied. The `u` argument is equal to `1 - t`.

The function is called repeatedly _before_ the transition begins, with different `t` and `u` arguments.

```svelte
<!--- file: App.svelte --->
<script>
	import { elasticOut } from 'svelte/easing';

	/** @type {boolean} */
	export let visible;

	/**
	 * @param {HTMLElement} node
	 * @param {{ delay?: number, duration?: number, easing?: (t: number) => number }} params
	 */
	function whoosh(node, params) {
		const existingTransform = getComputedStyle(node).transform.replace('none', '');

		return {
			delay: params.delay || 0,
			duration: params.duration || 400,
			easing: params.easing || elasticOut,
			css: (t, u) => `transform: ${existingTransform} scale(${t})`
		};
	}
</script>

{#if visible}
	<div in:whoosh>whooshes in</div>
{/if}
```

A custom transition function can also return a `tick` function, which is called _during_ the transition with the same `t` and `u` arguments.

> [!NOTE] If it's possible to use `css` instead of `tick`, do so — web animations can run off the main thread, preventing jank on slower devices.

```svelte
<!--- file: App.svelte --->
<script>
	export let visible = false;

	/**
	 * @param {HTMLElement} node
	 * @param {{ speed?: number }} params
	 */
	function typewriter(node, { speed = 1 }) {
		const valid = node.childNodes.length === 1 && node.childNodes[0].nodeType === Node.TEXT_NODE;

		if (!valid) {
			throw new Error(`This transition only works on elements with a single text node child`);
		}

		const text = node.textContent;
		const duration = text.length / (speed * 0.01);

		return {
			duration,
			tick: (t) => {
				const i = ~~(text.length * t);
				node.textContent = text.slice(0, i);
			}
		};
	}
</script>

{#if visible}
	<p in:typewriter={{ speed: 1 }}>The quick brown fox jumps over the lazy dog</p>
{/if}
```

If a transition returns a function instead of a transition object, the function will be called in the next microtask. This allows multiple transitions to coordinate, making [crossfade effects](/tutorial/deferred-transitions) possible.

Transition functions also receive a third argument, `options`, which contains information about the transition.

Available values in the `options` object are:

- `direction` - one of `in`, `out`, or `both` depending on the type of transition

## Transition events

An element with transitions will dispatch the following events in addition to any standard DOM events:

- `introstart`
- `introend`
- `outrostart`
- `outroend`

```svelte
{#if visible}
	<p
		transition:fly={{ y: 200, duration: 2000 }}
		onintrostart={() => (status = 'intro started')}
		onoutrostart={() => (status = 'outro started')}
		onintroend={() => (status = 'intro ended')}
		onoutroend={() => (status = 'outro ended')}
	>
		Flies in and out
	</p>
{/if}
```

## LEARN SVELTE5 in and OUTE

## title: in: and out:

The `in:` and `out:` directives are identical to [`transition:`](transition), except that the resulting transitions are not bidirectional — an `in` transition will continue to 'play' alongside the `out` transition, rather than reversing, if the block is outroed while the transition is in progress. If an out transition is aborted, transitions will restart from scratch.

```svelte
<script>
	import { fade, fly } from 'svelte/transition';

	let visible = $state(false);
</script>

<label>
	<input type="checkbox" bind:checked={visible} />
	visible
</label>

{#if visible}
	<div in:fly={{ y: 200 }} out:fade>flies in, fades out</div>
{/if}
```

LEARN SVELTE5 Animate

---

## title: animate:

An animation is triggered when the contents of a [keyed each block](each#Keyed-each-blocks) are re-ordered. Animations do not run when an element is added or removed, only when the index of an existing data item within the each block changes. Animate directives must be on an element that is an _immediate_ child of a keyed each block.

Animations can be used with Svelte's [built-in animation functions](svelte-animate) or [custom animation functions](#Custom-animation-functions).

```svelte
<!-- When `list` is reordered the animation will run -->
{#each list as item, index (item)}
	<li animate:flip>{item}</li>
{/each}
```

## Animation Parameters

As with actions and transitions, animations can have parameters.

(The double `{{curlies}}` aren't a special syntax; this is an object literal inside an expression tag.)

```svelte
{#each list as item, index (item)}
	<li animate:flip={{ delay: 500 }}>{item}</li>
{/each}
```

## Custom animation functions

```js
/// copy: false
// @noErrors
animation = (node: HTMLElement, { from: DOMRect, to: DOMRect } , params: any) => {
	delay?: number,
	duration?: number,
	easing?: (t: number) => number,
	css?: (t: number, u: number) => string,
	tick?: (t: number, u: number) => void
}
```

Animations can use custom functions that provide the `node`, an `animation` object and any `parameters` as arguments. The `animation` parameter is an object containing `from` and `to` properties each containing a [DOMRect](https://developer.mozilla.org/en-US/docs/Web/API/DOMRect#Properties) describing the geometry of the element in its `start` and `end` positions. The `from` property is the DOMRect of the element in its starting position, and the `to` property is the DOMRect of the element in its final position after the list has been reordered and the DOM updated.

If the returned object has a `css` method, Svelte will create a [web animation](https://developer.mozilla.org/en-US/docs/Web/API/Web_Animations_API) that plays on the element.

The `t` argument passed to `css` is a value that goes from `0` and `1` after the `easing` function has been applied. The `u` argument is equal to `1 - t`.

The function is called repeatedly _before_ the animation begins, with different `t` and `u` arguments.

<!-- TODO: Types -->

```svelte
<!--- file: App.svelte --->
<script>
	import { cubicOut } from 'svelte/easing';

	/**
	 * @param {HTMLElement} node
	 * @param {{ from: DOMRect; to: DOMRect }} states
	 * @param {any} params
	 */
	function whizz(node, { from, to }, params) {
		const dx = from.left - to.left;
		const dy = from.top - to.top;

		const d = Math.sqrt(dx * dx + dy * dy);

		return {
			delay: 0,
			duration: Math.sqrt(d) * 120,
			easing: cubicOut,
			css: (t, u) => `transform: translate(${u * dx}px, ${u * dy}px) rotate(${t * 360}deg);`
		};
	}
</script>

{#each list as item, index (item)}
	<div animate:whizz>{item}</div>
{/each}
```

A custom animation function can also return a `tick` function, which is called _during_ the animation with the same `t` and `u` arguments.

> [!NOTE] If it's possible to use `css` instead of `tick`, do so — web animations can run off the main thread, preventing jank on slower devices.

```svelte
<!--- file: App.svelte --->
<script>
	import { cubicOut } from 'svelte/easing';

	/**
	 * @param {HTMLElement} node
	 * @param {{ from: DOMRect; to: DOMRect }} states
	 * @param {any} params
	 */
	function whizz(node, { from, to }, params) {
		const dx = from.left - to.left;
		const dy = from.top - to.top;

		const d = Math.sqrt(dx * dx + dy * dy);

		return {
			delay: 0,
			duration: Math.sqrt(d) * 120,
			easing: cubicOut,
			tick: (t, u) => Object.assign(node.style, { color: t > 0.5 ? 'Pink' : 'Blue' })
		};
	}
</script>

{#each list as item, index (item)}
	<div animate:whizz>{item}</div>
{/each}
```

Learn Svelte Window

---

## title: <svelte:window>

```svelte
<svelte:window onevent={handler} />
```

```svelte
<svelte:window bind:prop={value} />
```

The `<svelte:window>` element allows you to add event listeners to the `window` object without worrying about removing them when the component is destroyed, or checking for the existence of `window` when server-side rendering.

This element may only appear at the top level of your component — it cannot be inside a block or element.

```svelte
<script>
	function handleKeydown(event) {
		alert(`pressed the ${event.key} key`);
	}
</script>

<svelte:window onkeydown={handleKeydown} />
```

You can also bind to the following properties:

- `innerWidth`
- `innerHeight`
- `outerWidth`
- `outerHeight`
- `scrollX`
- `scrollY`
- `online` — an alias for `window.navigator.onLine`
- `devicePixelRatio`

All except `scrollX` and `scrollY` are readonly.

```svelte
<svelte:window bind:scrollY={y} />
```

> [!NOTE] Note that the page will not be scrolled to the initial value to avoid accessibility issues. Only subsequent changes to the bound variable of `scrollX` and `scrollY` will cause scrolling. If you have a legitimate reason to scroll when the component is rendered, call `scrollTo()` in an `$effect`.

Learn Svelte Document

---

## title: <svelte:document>

```svelte
<svelte:document onevent={handler} />
```

```svelte
<svelte:document bind:prop={value} />
```

Similarly to `<svelte:window>`, this element allows you to add listeners to events on `document`, such as `visibilitychange`, which don't fire on `window`. It also lets you use [actions](use) on `document`.

As with `<svelte:window>`, this element may only appear the top level of your component and must never be inside a block or element.

```svelte
<svelte:document onvisibilitychange={handleVisibilityChange} use:someAction />
```

You can also bind to the following properties:

- `activeElement`
- `fullscreenElement`
- `pointerLockElement`
- `visibilityState`

All are readonly.

Learn Svelte Body

---

## title: <svelte:body>

```svelte
<svelte:body onevent={handler} />
```

Similarly to `<svelte:window>`, this element allows you to add listeners to events on `document.body`, such as `mouseenter` and `mouseleave`, which don't fire on `window`. It also lets you use [actions](use) on the `<body>` element.

As with `<svelte:window>` and `<svelte:document>`, this element may only appear the top level of your component and must never be inside a block or element.

```svelte
<svelte:body onmouseenter={handleMouseenter} onmouseleave={handleMouseleave} use:someAction />
```

Learn svelte/animate

flip
The flip function calculates the start and end position of an element and animates between them, translating the x and y values. flip stands for First, Last, Invert, Play.

function flip(
node: Element,
{
from,
to
}: {
from: DOMRect;
to: DOMRect;
},
params?: FlipParams
): AnimationConfig;
AnimationConfig
interface AnimationConfig {…}
delay?: number;
duration?: number;
easing?: (t: number) => number;
css?: (t: number, u: number) => string;
tick?: (t: number, u: number) => void;
FlipParams
interface FlipParams {…}
delay?: number;
duration?: number | ((len: number) => number);
easing?: (t: number) => number;

Learn svelte/easing

import {
backIn,
backInOut,
backOut,
bounceIn,
bounceInOut,
bounceOut,
circIn,
circInOut,
circOut,
cubicIn,
cubicInOut,
cubicOut,
elasticIn,
elasticInOut,
elasticOut,
expoIn,
expoInOut,
expoOut,
linear,
quadIn,
quadInOut,
quadOut,
quartIn,
quartInOut,
quartOut,
quintIn,
quintInOut,
quintOut,
sineIn,
sineInOut,
sineOut
} from 'svelte/easing';
backIn
function backIn(t: number): number;
backInOut
function backInOut(t: number): number;
backOut
function backOut(t: number): number;
bounceIn
function bounceIn(t: number): number;
bounceInOut
function bounceInOut(t: number): number;
bounceOut
function bounceOut(t: number): number;
circIn
function circIn(t: number): number;
circInOut
function circInOut(t: number): number;
circOut
function circOut(t: number): number;
cubicIn
function cubicIn(t: number): number;
cubicInOut
function cubicInOut(t: number): number;
cubicOut
function cubicOut(t: number): number;
elasticIn
function elasticIn(t: number): number;
elasticInOut
function elasticInOut(t: number): number;
elasticOut
function elasticOut(t: number): number;
expoIn
function expoIn(t: number): number;
expoInOut
function expoInOut(t: number): number;
expoOut
function expoOut(t: number): number;
linear
function linear(t: number): number;
quadIn
function quadIn(t: number): number;
quadInOut
function quadInOut(t: number): number;
quadOut
function quadOut(t: number): number;
quartIn
function quartIn(t: number): number;
quartInOut
function quartInOut(t: number): number;
quartOut
function quartOut(t: number): number;
quintIn
function quintIn(t: number): number;
quintInOut
function quintInOut(t: number): number;
quintOut
function quintOut(t: number): number;
sineIn
function sineIn(t: number): number;
sineInOut
function sineInOut(t: number): number;
sineOut
function sineOut(t: number): number;

Learn svelte/events

import { on } from 'svelte/events';
on
Attaches an event handler to the window and returns a function that removes the handler. Using this rather than addEventListener will preserve the correct order relative to handlers added declaratively (with attributes like onclick), which use event delegation for performance reasons

function on<Type extends keyof WindowEventMap>(
window: Window,
type: Type,
handler: (
this: Window,
event: WindowEventMap[Type]
) => any,
options?: AddEventListenerOptions | undefined
): () => void;
function on<Type extends keyof DocumentEventMap>(
document: Document,
type: Type,
handler: (
this: Document,
event: DocumentEventMap[Type]
) => any,
options?: AddEventListenerOptions | undefined
): () => void;
function on<
Element extends HTMLElement,
Type extends keyof HTMLElementEventMap

> (

    element: Element,
    type: Type,
    handler: (
    	this: Element,
    	event: HTMLElementEventMap[Type]
    ) => any,
    options?: AddEventListenerOptions | undefined

): () => void;
function on<
Element extends MediaQueryList,
Type extends keyof MediaQueryListEventMap

> (

    element: Element,
    type: Type,
    handler: (
    	this: Element,
    	event: MediaQueryListEventMap[Type]
    ) => any,
    options?: AddEventListenerOptions | undefined

): () => void;
function on(
element: EventTarget,
type: string,
handler: EventListener,
options?: AddEventListenerOptions | undefined
): () => void;

Learn Svelte motion

import {
Spring,
Tween,
prefersReducedMotion,
spring,
tweened
} from 'svelte/motion';
Spring
Available since 5.8.0

A wrapper for a value that behaves in a spring-like fashion. Changes to spring.target will cause spring.current to move towards it over time, taking account of the spring.stiffness and spring.damping parameters.

<script>
	import { Spring } from 'svelte/motion';

	const spring = new Spring(0);
</script>

<input type="range" bind:value={spring.target} />
<input type="range" bind:value={spring.current} disabled />
class Spring<T> {…}
constructor(value: T, options?: SpringOpts);
static of<U>(fn: () => U, options?: SpringOpts): Spring<U>;
Create a spring whose value is bound to the return value of fn. This must be called inside an effect root (for example, during component initialisation).

<script>
	import { Spring } from 'svelte/motion';

	let { number } = $props();

	const spring = Spring.of(() => number);
</script>

set(value: T, options?: SpringUpdateOpts): Promise<void>;
Sets spring.target to value and returns a Promise that resolves if and when spring.current catches up to it.

If options.instant is true, spring.current immediately matches spring.target.

If options.preserveMomentum is provided, the spring will continue on its current trajectory for the specified number of milliseconds. This is useful for things like ‘fling’ gestures.

damping: number;
precision: number;
stiffness: number;
target: T;
The end value of the spring. This property only exists on the Spring class, not the legacy spring store.

get current(): T;
The current value of the spring. This property only exists on the Spring class, not the legacy spring store.

Tween
Available since 5.8.0

A wrapper for a value that tweens smoothly to its target value. Changes to tween.target will cause tween.current to move towards it over time, taking account of the delay, duration and easing options.

<script>
	import { Tween } from 'svelte/motion';

	const tween = new Tween(0);
</script>

<input type="range" bind:value={tween.target} />
<input type="range" bind:value={tween.current} disabled />
class Tween<T> {…}
static of<U>(fn: () => U, options?: TweenedOptions<U> | undefined): Tween<U>;
Create a tween whose value is bound to the return value of fn. This must be called inside an effect root (for example, during component initialisation).

<script>
	import { Tween } from 'svelte/motion';

	let { number } = $props();

	const tween = Tween.of(() => number);
</script>

constructor(value: T, options?: TweenedOptions<T>);
set(value: T, options?: TweenedOptions<T> | undefined): Promise<void>;
Sets tween.target to value and returns a Promise that resolves if and when tween.current catches up to it.

If options are provided, they will override the tween’s defaults.

get current(): T;
set target(v: T);
get target(): T;
prefersReducedMotion
Available since 5.7.0

A media query that matches if the user prefers reduced motion.

<script>
	import { prefersReducedMotion } from 'svelte/motion';
	import { fly } from 'svelte/transition';

	let visible = $state(false);
</script>

<button onclick={() => visible = !visible}>
toggle
</button>

{#if visible}
<p transition:fly={{ y: prefersReducedMotion.current ? 0 : 200 }}>
flies in, unless the user prefers reduced motion
</p>
{/if}
const prefersReducedMotion: MediaQuery;
spring
Use Spring instead

The spring function in Svelte creates a store whose value is animated, with a motion that simulates the behavior of a spring. This means when the value changes, instead of transitioning at a steady rate, it “bounces” like a spring would, depending on the physics parameters provided. This adds a level of realism to the transitions and can enhance the user experience.

function spring<T = any>(
value?: T | undefined,
opts?: SpringOpts | undefined
): Spring<T>;
tweened
Use Tween instead

A tweened store in Svelte is a special type of store that provides smooth transitions between state values over time.

function tweened<T>(
value?: T | undefined,
defaults?: TweenedOptions<T> | undefined
): Tweened<T>;
Spring
interface Spring<T> extends Readable<T> {…}
set(new_value: T, opts?: SpringUpdateOpts): Promise<void>;
update: (fn: Updater<T>, opts?: SpringUpdateOpts) => Promise<void>;
deprecated Only exists on the legacy spring store, not the Spring class
subscribe(fn: (value: T) => void): Unsubscriber;
deprecated Only exists on the legacy spring store, not the Spring class
precision: number;
damping: number;
stiffness: number;
Tweened
interface Tweened<T> extends Readable<T> {…}
set(value: T, opts?: TweenedOptions<T>): Promise<void>;
update(updater: Updater<T>, opts?: TweenedOptions<T>): Promise<void>;

Learn svelte/reactivity/window

This module exports reactive versions of various window values, each of which has a reactive current property that you can reference in reactive contexts (templates, deriveds and effects) without using <svelte:window> bindings or manually creating your own event listeners.

<script>
	import { innerWidth, innerHeight } from 'svelte/reactivity/window';
</script>

<p>{innerWidth.current}x{innerHeight.current}</p>

import {
devicePixelRatio,
innerHeight,
innerWidth,
online,
outerHeight,
outerWidth,
screenLeft,
screenTop,
scrollX,
scrollY
} from 'svelte/reactivity/window';
devicePixelRatio
Available since 5.11.0

devicePixelRatio.current is a reactive view of window.devicePixelRatio. On the server it is undefined. Note that behaviour differs between browsers — on Chrome it will respond to the current zoom level, on Firefox and Safari it won’t.

const devicePixelRatio: {
get current(): number | undefined;
};
innerHeight
Available since 5.11.0

innerHeight.current is a reactive view of window.innerHeight. On the server it is undefined.

const innerHeight: ReactiveValue<number | undefined>;
innerWidth
Available since 5.11.0

innerWidth.current is a reactive view of window.innerWidth. On the server it is undefined.

const innerWidth: ReactiveValue<number | undefined>;
online
Available since 5.11.0

online.current is a reactive view of navigator.onLine. On the server it is undefined.

const online: ReactiveValue<boolean | undefined>;
outerHeight
Available since 5.11.0

outerHeight.current is a reactive view of window.outerHeight. On the server it is undefined.

const outerHeight: ReactiveValue<number | undefined>;
outerWidth
Available since 5.11.0

outerWidth.current is a reactive view of window.outerWidth. On the server it is undefined.

const outerWidth: ReactiveValue<number | undefined>;
screenLeft
Available since 5.11.0

screenLeft.current is a reactive view of window.screenLeft. It is updated inside a requestAnimationFrame callback. On the server it is undefined.

const screenLeft: ReactiveValue<number | undefined>;
screenTop
Available since 5.11.0

screenTop.current is a reactive view of window.screenTop. It is updated inside a requestAnimationFrame callback. On the server it is undefined.

const screenTop: ReactiveValue<number | undefined>;
scrollX
Available since 5.11.0

scrollX.current is a reactive view of window.scrollX. On the server it is undefined.

const scrollX: ReactiveValue<number | undefined>;
scrollY
Available since 5.11.0

scrollY.current is a reactive view of window.scrollY. On the server it is undefined.

const scrollY: ReactiveValue<number | undefined>;

Learn svelte/reactivity

Svelte provides reactive versions of various built-ins like SvelteMap, SvelteSet and SvelteURL. These can be imported from svelte/reactivity and used just like their native counterparts.

<script>
	import { SvelteURL } from 'svelte/reactivity';

	const url = new SvelteURL('https://example.com/path');
</script>

<!-- changes to these... -->
<input bind:value={url.protocol} />
<input bind:value={url.hostname} />
<input bind:value={url.pathname} />

<hr />

<!-- will update `href` and vice versa -->
<input bind:value={url.href} />

import {
MediaQuery,
SvelteDate,
SvelteMap,
SvelteSet,
SvelteURL,
SvelteURLSearchParams,
createSubscriber
} from 'svelte/reactivity';
MediaQuery
Available since 5.7.0

Creates a media query and provides a current property that reflects whether or not it matches.

Use it carefully — during server-side rendering, there is no way to know what the correct value should be, potentially causing content to change upon hydration. If you can use the media query in CSS to achieve the same effect, do that.

<script>
	import { MediaQuery } from 'svelte/reactivity';

	const large = new MediaQuery('min-width: 800px');
</script>

<h1>{large.current ? 'large screen' : 'small screen'}</h1>
class MediaQuery extends ReactiveValue<boolean> {…}
constructor(query: string, fallback?: boolean | undefined);
query A media query string
fallback Fallback value for the server
SvelteDate
class SvelteDate extends Date {…}
constructor(...params: any[]);
SvelteMap
class SvelteMap<K, V> extends Map<K, V> {…}
constructor(value?: Iterable<readonly [K, V]> | null | undefined);
set(key: K, value: V): this;
SvelteSet
class SvelteSet<T> extends Set<T> {…}
constructor(value?: Iterable<T> | null | undefined);
add(value: T): this;
SvelteURL
class SvelteURL extends URL {…}
get searchParams(): SvelteURLSearchParams;
SvelteURLSearchParams
class SvelteURLSearchParams extends URLSearchParams {…}
[REPLACE](params: URLSearchParams): void;
createSubscriber
Available since 5.7.0

Returns a subscribe function that, if called in an effect (including expressions in the template), calls its start callback with an update function. Whenever update is called, the effect re-runs.

If start returns a function, it will be called when the effect is destroyed.

If subscribe is called in multiple effects, start will only be called once as long as the effects are active, and the returned teardown function will only be called when all effects are destroyed.

It’s best understood with an example. Here’s an implementation of MediaQuery:

import { createSubscriber } from 'svelte/reactivity';
import { on } from 'svelte/events';

export class MediaQuery {
#query;
#subscribe;

    constructor(query) {
    	this.#query = window.matchMedia(`(${query})`);

    	this.#subscribe = createSubscriber((update) => {
    		// when the `change` event occurs, re-run any effects that read `this.current`
    		const off = on(this.#query, 'change', update);

    		// stop listening when all the effects are destroyed
    		return () => off();
    	});
    }

    get current() {
    	this.#subscribe();

    	// Return the current state of the query, whether or not we're in an effect
    	return this.#query.matches;
    }

}
function createSubscriber(
start: (update: () => void) => (() => void) | void
): () => void;

Learn Svelte Transition

import {
blur,
crossfade,
draw,
fade,
fly,
scale,
slide
} from 'svelte/transition';
blur
Animates a blur filter alongside an element’s opacity.

function blur(
node: Element,
{
delay,
duration,
easing,
amount,
opacity
}?: BlurParams | undefined
): TransitionConfig;
crossfade
The crossfade function creates a pair of transitions called send and receive. When an element is ‘sent’, it looks for a corresponding element being ‘received’, and generates a transition that transforms the element to its counterpart’s position and fades it out. When an element is ‘received’, the reverse happens. If there is no counterpart, the fallback transition is used.

function crossfade({
fallback,
...defaults
}: CrossfadeParams & {
fallback?: (
node: Element,
params: CrossfadeParams,
intro: boolean
) => TransitionConfig;
}): [
(
node: any,
params: CrossfadeParams & {
key: any;
}
) => () => TransitionConfig,
(
node: any,
params: CrossfadeParams & {
key: any;
}
) => () => TransitionConfig
];
draw
Animates the stroke of an SVG element, like a snake in a tube. in transitions begin with the path invisible and draw the path to the screen over time. out transitions start in a visible state and gradually erase the path. draw only works with elements that have a getTotalLength method, like <path> and <polyline>.

function draw(
node: SVGElement & {
getTotalLength(): number;
},
{
delay,
speed,
duration,
easing
}?: DrawParams | undefined
): TransitionConfig;
fade
Animates the opacity of an element from 0 to the current opacity for in transitions and from the current opacity to 0 for out transitions.

function fade(
node: Element,
{ delay, duration, easing }?: FadeParams | undefined
): TransitionConfig;
fly
Animates the x and y positions and the opacity of an element. in transitions animate from the provided values, passed as parameters to the element’s default values. out transitions animate from the element’s default values to the provided values.

function fly(
node: Element,
{
delay,
duration,
easing,
x,
y,
opacity
}?: FlyParams | undefined
): TransitionConfig;
scale
Animates the opacity and scale of an element. in transitions animate from the provided values, passed as parameters, to an element’s current (default) values. out transitions animate from an element’s default values to the provided values.

function scale(
node: Element,
{
delay,
duration,
easing,
start,
opacity
}?: ScaleParams | undefined
): TransitionConfig;
slide
Slides an element in and out.

function slide(
node: Element,
{
delay,
duration,
easing,
axis
}?: SlideParams | undefined
): TransitionConfig;
BlurParams
interface BlurParams {…}
delay?: number;
duration?: number;
easing?: EasingFunction;
amount?: number | string;
opacity?: number;
CrossfadeParams
interface CrossfadeParams {…}
delay?: number;
duration?: number | ((len: number) => number);
easing?: EasingFunction;
DrawParams
interface DrawParams {…}
delay?: number;
speed?: number;
duration?: number | ((len: number) => number);
easing?: EasingFunction;
EasingFunction
type EasingFunction = (t: number) => number;
FadeParams
interface FadeParams {…}
delay?: number;
duration?: number;
easing?: EasingFunction;
FlyParams
interface FlyParams {…}
delay?: number;
duration?: number;
easing?: EasingFunction;
x?: number | string;
y?: number | string;
opacity?: number;
ScaleParams
interface ScaleParams {…}
delay?: number;
duration?: number;
easing?: EasingFunction;
start?: number;
opacity?: number;
SlideParams
interface SlideParams {…}
delay?: number;
duration?: number;
easing?: EasingFunction;
axis?: 'x' | 'y';
TransitionConfig
interface TransitionConfig {…}
delay?: number;
duration?: number;
easing?: EasingFunction;
css?: (t: number, u: number) => string;
tick?: (t: number, u: number) => void;
