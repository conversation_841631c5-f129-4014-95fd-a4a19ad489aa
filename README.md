# Arise Transit

A modern web application built with SvelteKit, Svelte 5, and daisyUI for urban transportation solutions.

## Overview

Arise Transit is a responsive web application designed to address urban transportation challenges. Built with the latest web technologies, it provides an elegant and efficient user experience across all devices.

## Technology Stack

- **Framework**: SvelteKit with Svelte 5 runes
- **Styling**: Tailwind CSS v4 with daisyUI 5
- **Package Manager**: Bun
- **Testing**: Vitest
- **Type Safety**: TypeScript
- **Form Handling**: SvelteKit-SuperForms with Zod validation
- **Error Monitoring**: Sentry integration for error tracking and performance monitoring

## Development

### Prerequisites

- Node.js (LTS version)
- Bun package manager

### Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   bun install
   ```
3. Start the development server:

   ```bash
   bun run dev
   ```

   Or open in a new browser tab:

   ```bash
   bun run dev -- --open
   ```

### Available Commands

- 🚀 **Dev Server**: `bun run dev`
- 🏗️ **Build**: `bun run build`
- 🔍 **Lint**: `bun run lint` (<PERSON><PERSON><PERSON> + Prettier)
- ✨ **Format**: `bun run format` (Prettier)
- ✅ **Type Check**: `bun run check`
- 🧪 **Test**: `bun run test`
- 🧪 **Test Specific File**: `bun run test:unit src/path/to/file.test.ts`

## Code Style Guidelines

- **Components**: Use PascalCase for component files with Svelte 5 runes ($state, $effect)
- **Naming**: PascalCase for components/types, camelCase for variables/functions
- **Types**: Define TypeScript interfaces for props with JSDoc documentation
- **Imports**: Group imports (app → lib → external), use $lib aliasing
- **Styling**: Use Tailwind with DaisyUI component framework
- **Error Handling**: Try/catch blocks with graceful fallbacks
- **Accessibility**: ARIA attributes, semantic HTML, keyboard navigation
- **Performance**: Lazy loading, responsive design, SSR-safe code
- **File Organization**: Components in /lib/components, utilities in /lib/utils

## Responsive Design

The application uses a custom viewport utility for responsive design. See [Viewport Usage Guide](docs/viewport-usage-guide.md) for detailed documentation on implementing responsive components.

## Form Handling with SuperForms

Arise Transit uses SvelteKit-SuperForms for form handling and validation:

- **Schema-based validation**: Forms are validated using Zod schemas
- **Client-side validation**: Real-time validation feedback without page reloads
- **Server-side validation**: Secure validation on form submission
- **Status messages**: Display success/error messages after form submission
- **Form state management**: Track form submission state, errors, and constraints

Key points to remember when working with forms:

- Success messages don't need a status code (defaults to 200)
- Error messages should use status codes in the 400-599 range
- Use the `superForm` client function to initialize forms
- Use the `superValidate` and `message` server functions to process forms

## Error Monitoring and Performance Tracking

Arise Transit integrates with Sentry for comprehensive error tracking and performance monitoring:

- **Client-side error tracking**: Automatically captures unhandled JavaScript errors
- **Server-side error tracking**: Captures server-side exceptions and 404/500 errors
- **Performance monitoring**: Tracks page load performance and navigation
- **Session replay**: Records user sessions when errors occur for better debugging
- **Custom error handling**: Utility functions in `src/lib/utils/sentry.ts`

Test and debug the Sentry integration by visiting the `/sentry-debug` route in the application.
## SEO and Meta Tags

This project utilizes the `svelte-meta-tags` library for managing SEO meta tags effectively. The strategy involves setting base tags globally and allowing individual pages to override them as needed.

### Base Meta Tags

Default meta tags (like site title, general description, base URL, default OG image) are defined in the `baseMetaTags` object within the root layout's server load function:

```typescript
// src/routes/+layout.server.ts
export const load: LayoutServerLoad = async ({ url }) => {
  const baseMetaTags = {
    title: 'Arise Transit - Your Premier Transportation Service',
    description: 'Arise Transit provides luxury transportation services...',
    url: url.href,
    ogImage: '/placeholder-og-image.png'
  };
  return { baseMetaTags /* ... other data */ };
};
```

### Page-Specific Overrides

The root layout component (`src/routes/+layout.svelte`) uses the `deepMerge` utility from `svelte-meta-tags` to combine these base tags with any page-specific tags provided through `$page.data.pageMetaTags`:

```svelte
<!-- src/routes/+layout.svelte -->
<script lang="ts">
  import { MetaTags, deepMerge } from 'svelte-meta-tags';
  import { page } from '$app/stores';
  import type { LayoutData } from './$types';

  let { data }: { data: LayoutData } = $props();
  let metaTags = $derived(deepMerge(data.baseMetaTags, $page.data.pageMetaTags || {}));
</script>

<MetaTags {...metaTags} />
```

To override or add meta tags for a specific page (e.g., `/about`), define a `pageMetaTags` object in that page's load function (`+page.ts` or `+page.server.ts`):

```typescript
// src/routes/about/+page.ts (example)
import type { PageLoad } from './$types';

export const load: PageLoad = async () => {
  const pageMetaTags = {
    title: 'About Us - Arise Transit',
    description: 'Learn more about Arise Transit and our commitment to quality service.',
    // Other specific tags like 'keywords' can be added here
  };
  return { pageMetaTags };
};
```

The `deepMerge` function ensures that only the specified tags are overridden, while the base tags are retained for any properties not defined in `pageMetaTags`.


## Context Priming

Read README.md, CLAUDE.md and run git ls-files to understand this codebase

## Building for Production

To create a production build:

```bash
bun run build
```

Preview the production build:

```bash
bun run preview
```

## License

[License details]
