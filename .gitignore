node_modules

# Output
.output
.vercel
.netlify
.wrangler
/.svelte-kit
/build

# OS
.DS_Store
Thumbs.db

# Env
.env
.env.*
!.env.example
!.env.test

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*
.clinerules
.roomodes
.windsurfrules

# AI assistant files
.aider*
.aider.conf.yml
.aider.chat.history.md
.aider.input.history
.aider.tags.cache.v4/cache.db

# RA-Aid files
.ra-aid/

# Sentry Config File
.env.sentry-build-plugin
.roo/rules-devops/rules.md
.kilocode/mcp.json
.roo/mcp.json
.roo/mcp.json
.roo/mcp.json
