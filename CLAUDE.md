# Arise Transit Development Guide

## Commands

- 🚀 **Dev Server**: `bun run dev`
- 🏗️ **Build**: `bun run build`
- 🔍 **Lint**: `bun run lint` (ESLint + Prettier)
- ✨ **Format**: `bun run format` (Prettier)
- ✅ **Type Check**: `bun run check`
- 🧪 **Test**: `bun run test`
- 🧪 **Test Specific File**: `bun run test:unit src/path/to/file.test.ts`

## Code Style Guidelines

- **Components**: Use PascalCase for component files with Svelte 5 runes ($state, $effect)
- **Naming**: PascalCase for components/types, camelCase for variables/functions
- **Types**: Define TypeScript interfaces for props with JSDoc documentation
- **Imports**: Group imports (app → lib → external), use $lib aliasing
- **Styling**: Use Tailwind with DaisyUI component framework
- **Error Handling**: Use Sentry utilities for error tracking along with try/catch blocks for graceful fallbacks
- **Accessibility**: ARIA attributes, semantic HTML, keyboard navigation
- **Performance**: Lazy loading, responsive design, SSR-safe code
- **File Organization**: Components in /lib/components, utilities in /lib/utils
- **State Management**: Prefer $state runes for both local and global state; use stores for complex async data streams or when manual control is needed

## Form Handling with SuperForms

### Implementation Pattern

1. **Schema Definition**: Define a Zod schema for form validation
   ```typescript
   // schema.ts
   import { z } from 'zod';

   export const schema = z.object({
     name: z.string().min(1, "Name is required"),
     email: z.string().email("Please enter a valid email address"),
     // Add more fields as needed
   });
   ```

2. **Server-Side Setup**: Initialize and validate forms in page.server.ts
   ```typescript
   // +page.server.ts
   import { superValidate, message as setMessage } from 'sveltekit-superforms';
   import { zod } from 'sveltekit-superforms/adapters';
   import { fail } from '@sveltejs/kit';
   import { schema } from './schema';

   export const load = (async () => {
     // Initialize an empty form
     const form = await superValidate(zod(schema));
     return { form };
   });

   export const actions = {
     default: async ({ request }) => {
       // Validate the form
       const form = await superValidate(request, zod(schema));

       if (!form.valid) {
         return fail(400, { form });
       }

       try {
         // Process form data...

         // Return success message (no status needed for success)
         return setMessage(form, "Success message");
       } catch (error) {
         // Return error message with error status
         return setMessage(form, 'Error message', { status: 500 });
       }
     }
   };
   ```

3. **Client-Side Implementation**: Use the form in your Svelte component
   ```svelte
   <script>
     import { superForm } from 'sveltekit-superforms/client';

     let { data } = $props();
     const { form, errors, constraints, enhance, message, submitting } = superForm(data.form, {
       // Options here
     });
   </script>

   {#if $message}
     <div class="message">{$message}</div>
   {/if}

   <form method="POST" use:enhance>
     <input
       bind:value={$form.name}
       class:error={$errors.name}
       {...$constraints.name}
     />
     {#if $errors.name}<span class="error">{$errors.name}</span>{/if}

     <!-- More form fields -->

     <button type="submit" disabled={$submitting}>
       {$submitting ? 'Submitting...' : 'Submit'}
     </button>
   </form>
   ```

### Important Notes

- **Status Codes**: For success messages, omit the status parameter (defaults to 200). For error messages, use status codes in the 400-599 range.
- **Form Validation**: SuperForms handles both client and server validation based on your Zod schema.
- **Form State**: The `superForm` function provides reactive stores for form state, errors, constraints, etc.
- **Progressive Enhancement**: The `enhance` action provides progressive enhancement for forms.

## Component System

### Key Components

#### Navigation Components

- **Header.svelte**: Main container for navigation, implements the daisyUI drawer pattern, manages scroll animations
- **Navigation.svelte**: Handles menu items and link interactions, adapts between desktop/mobile views

#### Content Components

- **Hero.svelte**: Hero component with multiple variants and responsive styling, supports named snippets
- **ResponsiveContainer.svelte**: Example of viewport utility implementation for responsive layouts

### Viewport Integration

- Use viewport utility consistently: always call functions with parentheses (e.g., `isDesktop()` not `isDesktop`)
- Store derived viewport values: `const isDesktopView = $derived(isDesktop())`
- For new responsive components, follow patterns in viewport-usage-guide.md

### Component Styling Patterns

1. **Component-Owned Styling**: Components should own their responsive styling logic

   ```typescript
   // Inside component
   const contentClass = $derived(isDesktopView ? 'desktop-style' : 'mobile-style');
   ```

2. **Prop-Based Customization**: Use props to customize component appearance

   ```typescript
   // Component API
   let { variant = 'default', size = 'medium' } = $props<{
   	variant?: 'default' | 'primary' | 'secondary';
   	size?: 'small' | 'medium' | 'large';
   }>();
   ```

3. **Named Snippets Pattern**: Use named snippets for structured content injection
   ```svelte
   <!-- In page.svelte -->
   <Hero variant="full" contentLayout="split">
   	{#snippet heading()}
   		<h1>Page Title</h1>
   	{/snippet}

   	{#snippet content()}
   		Main content here
   	{/snippet}

   	{#snippet cta()}
   		<span>Call to action</span>
   	{/snippet}
   </Hero>
   ```

### Mobile Drawer

- Based on daisyUI's drawer component with checkbox pattern
- Close drawer by setting checkbox to unchecked:
  ```js
  const drawer = document.getElementById('mobile-drawer') as HTMLInputElement;
  if (drawer) drawer.checked = false;
  ```
- Communication methods:
  1. Direct `onClose` prop passed to Navigation
  2. Context system using `setContext('closeDrawer', closeDrawer)`

### Hero Component Usage

#### Props API

- **Layout Props**: `variant`, `minHeight`, `contentWidth`, etc.
- **Styling Props**: `contentLayout`, `textSize`, `ctaStyle`, `ctaPosition`
- **Content Snippets**: `heading`, `content`, `cta`

#### Available Variants

- `"overlay"`: Text over background image with dark overlay
- `"centered"`: Centered content
- `"figure"`: Content with image on left
- `"figureReverse"`: Content with image on right
- `"full"`: Full-width background image

#### Example

```svelte
<Hero
  variant="full"
  contentLayout="split" // "default" | "centered" | "split"
  textSize="large"      // "default" | "large" | "small"
  ctaStyle="prominent"  // "default" | "prominent" | "subtle"
  ctaPosition="right"   // "default" | "right" | "center" | "bottom"
  ctaHref="/action-url"
>
  {#snippet heading()}
    <h1>Heading Content</h1>
  {/snippet}

  {#snippet content()}
    Paragraph content
  {/snippet}

  {#snippet cta()}
    <span>CTA Button Text</span>
  {/snippet}
</Hero>
```

### Component Interactions

1. Header sets up the drawer structure and provides context
2. Navigation receives mode prop ('desktop'/'mobile') and onClose function
3. Layout component coordinates everything through snippet props
4. Hero component handles its own responsive styling based on props and viewport

## Error Monitoring with Sentry

### Sentry Configuration

- **Integration Files**:
  - `src/hooks.client.ts`: Client-side Sentry configuration
  - `src/hooks.server.ts`: Server-side Sentry configuration with 404/500 error tracking
  - `src/routes/+error.svelte`: Custom error page with Sentry integration
  - `src/lib/utils/sentry.ts`: Utility functions for working with Sentry

### Using Sentry Utilities

To track errors and add context in your components:

```typescript
import { captureError, addBreadcrumb, identifyUser } from '$lib/utils/sentry';

// Track custom errors
try {
  // Risky operation
} catch (error) {
  captureError(error, {
    component: 'ComponentName',
    context: 'additional information'
  });
}

// Add breadcrumbs for important user actions
function handleImportantAction() {
  addBreadcrumb(
    'User performed important action',
    'user.action',
    'info',
    { actionId: 123 }
  );
}

// Identify users (only with their consent)
function onUserLogin(user) {
  identifyUser(user.id, user.username, {
    role: user.role
  });

## SEO and Meta Tag Management

The project uses `svelte-meta-tags` for managing page meta tags, crucial for SEO and social sharing.

### Global Base Tags (`+layout.server.ts`)

Default meta tags are defined in the root layout's server load function (`src/routes/+layout.server.ts`). These serve as the base for all pages.

```typescript
// src/routes/+layout.server.ts
import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ url }) => {
 const baseMetaTags = {
   title: 'Arise Transit - Default Title',
   description: 'Default description for Arise Transit services.',
   url: url.href, // Dynamically set the canonical URL
   ogImage: '/default-og-image.png' // Path to a default Open Graph image
   // Add other base tags like twitter:card, etc.
 };

 return {
   // ... other layout data
   baseMetaTags
 };
};
```

### Merging and Overriding Tags (`+layout.svelte`)

The root layout component (`src/routes/+layout.svelte`) merges the `baseMetaTags` with page-specific tags using `deepMerge` from `svelte-meta-tags`. Page-specific tags are expected in `$page.data.pageMetaTags`.

```svelte
<!-- src/routes/+layout.svelte -->
<script lang="ts">
 import { MetaTags, deepMerge } from 'svelte-meta-tags';
 import { page } from '$app/stores';
 import type { LayoutData } from './$types';

 let { data }: { data: LayoutData } = $props();

 // Merge base tags with page-specific tags (if any)
 let metaTags = $derived(deepMerge(data.baseMetaTags, $page.data.pageMetaTags || {}));
</script>

<!-- Render the merged meta tags -->
<MetaTags {...metaTags} />

<!-- Rest of the layout -->
```

### Page-Specific Tags (`+page.ts` or `+page.server.ts`)

To set or override meta tags for a specific route, return a `pageMetaTags` object from its load function.

```typescript
// Example: src/routes/about/+page.ts
import type { PageLoad } from './$types';

export const load: PageLoad = async () => {
 const pageMetaTags = {
   title: 'About Arise Transit', // Overrides base title
   description: 'Learn about our mission and services.', // Overrides base description
   keywords: 'luxury transport, dallas, about us' // Adds new tag
   // The 'url' and 'ogImage' from baseMetaTags will be retained unless overridden here
 };

 return {
   pageMetaTags
   // ... other page data
 };
};
```

This pattern ensures consistent base meta information across the site while allowing fine-grained control for individual pages.
}
```

### Error Page Customization

The custom error page (`+error.svelte`) handles both 404 and other errors. It includes:

- Random, friendly error messages based on error type
- Responsive design using the viewport utility
- Integration with Sentry for tracking and reporting
- Clean user interface with appropriate guidance

### CardCarousel Component Usage

#### Props API

- **`items`**: Array of objects that must include an `id` property
- **`cardContent`**: Named snippet for rendering card content
- **`cardAction`**: Named snippet for rendering card actions
- **`carouselClasses`**: Optional string of additional CSS classes for the carousel
- **`sectionVisible`**: Optional boolean to control animation timing

#### DaisyUI Integration

- Based on the DaisyUI carousel component with responsive design
- Automatically switches between stacked view (mobile) and carousel (desktop)
- Supports center-aligned cards with proper spacing
- Navigation controls follow DaisyUI patterns for consistency

#### Example

```svelte
<CardCarousel items={services} carouselClasses="bg-base-200" sectionVisible={inView}>
	{#snippet cardContent(service)}
		<figure>
			<img src={service.image} alt={service.title} />
		</figure>
		<div class="card-body">
			<h3 class="card-title">{service.title}</h3>
			<p>{service.description}</p>
		</div>
	{/snippet}

	{#snippet cardAction(service)}
		<a href={service.href} class="btn btn-primary">Learn More</a>
	{/snippet}
</CardCarousel>
```

#### Implementation Notes

- Navigation arrows use smooth transitions with accessibility features
- Numbered indicators provide better user orientation
- Items adapt responsively between desktop and mobile views
- CardCarousel handles all scroll behavior and animations internally
- The carousel uses DaisyUI's standard carousel class approach with center alignment
- For reliable positioning, carousel controls use the recommended DaisyUI absolute positioning pattern
