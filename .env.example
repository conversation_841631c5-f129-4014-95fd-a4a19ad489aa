# Database connection
DATABASE_URL="postgres://username:password@host:port/database?sslmode=require"
POSTGRES_PRISMA_URL="postgres://username:password@host:port/database?sslmode=require"
URL_NON_POOLING="postgres://username:password@host:port/database?sslmode=require"

# Supabase configuration
SUPABASE_URL="https://your-project-id.supabase.co"
PUBLIC_SUPABASE_URL="https://your-project-id.supabase.co"
SUPABASE_JWT_SECRET="your-jwt-secret"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# Postgres details
POSTGRES_USER="postgres"
POSTGRES_PASSWORD="your-password"
POSTGRES_DATABASE="postgres"
POSTGRES_HOST="your-host.supabase.co"
# DO NOT commit this file to your repository!
# The SENTRY_AUTH_TOKEN variable is picked up by the Sentry Build Plugin.
# It's used for authentication when uploading source maps.
# You can also set this env variable in your own `.env` files and remove this file.
SENTRY_AUTH_TOKEN="sentryauthtoken"

# For ISR (Incremental Static Regeneration) if needed
# BYPASS_TOKEN="your-random-token-at-least-32-chars-long"

# PostHog Key
PUBLIC_POSTHOG_KEY="your-posthog-key"
