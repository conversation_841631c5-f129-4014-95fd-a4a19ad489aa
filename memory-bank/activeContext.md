# Active Context

## Current Focus
- Monitoring performance impact of the CSS Parallax effect compared to the previous Three.js implementation.
- Consider adding subtle mouse interaction to `CssParallaxBackground.svelte` if desired.

## Recent Changes
- [2025-04-14 16:55:00] - Refactored viewport logic into a dedicated svelte.ts store (`viewport.svelte.ts`).
- [2025-04-14 16:58:00] - Updated Hero component to use the new viewport store for conditional rendering/styling.
- [2025-04-15 17:39:00] - Replaced custom WebGL sunrise effect with Three.js Sky shader implementation (`ThreeSunrise.svelte`) after multiple iterations to achieve desired artistic look (vibrant colors, low sun angle).
- [2025-04-15 16:47:13] - Fine-tuned `ThreeSunrise.svelte` parameters (<PERSON><PERSON>, Turbidity, Mie, Fog Color, Exposure) for improved color vibrancy and reduced haze based on visual feedback.

- [2025-04-15 16:51:23] - Adjusted sun's horizontal position (`theta`) in `ThreeSunrise.svelte` to `Math.PI / 2` (right side).
- [2025-04-15 16:52:06] - Further adjusted sun's horizontal position (`theta`) in `ThreeSunrise.svelte` to `Math.PI * 0.75` based on feedback (previous value was too far right).
- [2025-04-17 16:44:37] - Refined `ThreeSunrise.svelte` aesthetics: adjusted sky uniforms (turbidity, rayleigh, mie), fog (color, density), and lighting (sun, hemisphere) for a warmer, mistier orange-to-purple gradient with pink/yellow hints.
- [2025-04-17 17:00:32] - Refactored `ThreeSunrise.svelte` to remove local animation loop and rely solely on `animationStore` for rendering control.
- [2025-04-17 17:03:32] - Fixed Svelte 'state_referenced_locally' error in `ThreeSunrise.svelte` $effect hook by using local constants.
- [2025-04-17 18:27:37] - Created `CssParallaxBackground.svelte` with layered 3D CSS transforms reacting to scroll.
- [2025-04-17 18:27:37] - Integrated `CssParallaxBackground.svelte` into `Hero.svelte` as `backgroundEffect='css-parallax'`, replacing `ThreeSunrise`.
- [2025-04-17 18:37:53] - Refined styles and animation parameters in `CssParallaxBackground.svelte` for a subtle, premium abstract effect, replacing debug styles.

## Open Questions/Issues
- Monitor performance impact of the new CSS Parallax effect compared to the previous Three.js implementation.
- Consider adding subtle mouse interaction to `CssParallaxBackground.svelte` if desired.