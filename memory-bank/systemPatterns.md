# System Patterns

This document outlines the key architectural patterns, technologies, and guidelines used in the Arise Transit project, based on the project README.

## Technology Stack

- **Framework**: SvelteKit with Svelte 5 runes
- **Styling**: Tailwind CSS v4 with daisyUI 5
- **Package Manager**: Bun
- **Testing**: Vitest
- **Type Safety**: TypeScript
- **Form Handling**: SvelteKit-SuperForms with Zod validation
- **Error Monitoring**: Sentry integration for error tracking and performance monitoring

## Code Style Guidelines

- **Components**: Use PascalCase for component files with Svelte 5 runes ($state, $effect)
- **Naming**: PascalCase for components/types, camelCase for variables/functions
- **Types**: Define TypeScript interfaces for props with JSDoc documentation
- **Imports**: Group imports (app → lib → external), use $lib aliasing
- **Styling**: Use Tailwind with DaisyUI component framework
- **Error Handling**: Try/catch blocks with graceful fallbacks
- **Accessibility**: ARIA attributes, semantic HTML, keyboard navigation
- **Performance**: Lazy loading, responsive design, SSR-safe code
- **File Organization**: Components in `/lib/components`, utilities in `/lib/utils`

## Responsive Design

The application uses a custom viewport utility (`src/lib/utils/viewport.svelte.ts`) for responsive design. Refer to the [Viewport Usage Guide](docs/viewport-usage-guide.md) for implementation details.

## Form Handling with SuperForms

Arise Transit uses SvelteKit-SuperForms for robust form handling:
- **Schema-based validation**: Forms validated using Zod schemas.
- **Client-side validation**: Real-time feedback.
- **Server-side validation**: Secure validation on submission.
- **Status messages**: Display success/error messages.
- **Form state management**: Tracks submission state, errors, constraints.
- **Implementation**: Use `superForm` on the client, `superValidate` and `message` on the server.

## Error Monitoring and Performance Tracking (Sentry)

Integration with Sentry provides:
- Client-side error tracking (JavaScript errors).
- Server-side error tracking (exceptions, 404/500 errors).
- Performance monitoring (page load, navigation).
- Session replay for debugging.
- Custom error handling utilities in `src/lib/utils/sentry.ts`.
- Debug route: `/sentry-debug`.

## SEO and Meta Tags (`svelte-meta-tags`)

- **Strategy**: Base tags set globally in root layout (`+layout.server.ts`), overridden by specific pages (`+page.ts` or `+page.server.ts`).
- **Implementation**:
    - `baseMetaTags` defined in root `load` function.
    - `pageMetaTags` defined in page-specific `load` functions.
    - `deepMerge` utility combines base and page tags in root layout (`+layout.svelte`) using `<MetaTags>`.