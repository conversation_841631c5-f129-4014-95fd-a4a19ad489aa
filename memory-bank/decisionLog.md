[2025-04-15 16:48:00] - Decided to replace custom WebGL sunrise effect (`SunriseBackground.svelte`) with Three.js `Sky` shader (`ThreeSunrise.svelte`) due to difficulties achieving desired vibrant colors and artistic control with the custom shader.
[2025-04-15 17:30:00] - Final approach for Three.js `Sky` shader: Remove explicit sun mesh, rely on shader's internal sun, restrict sun elevation near horizon, use low Rayleigh & high Turbidity, tune exposure carefully, add fog. This followed expert feedback identifying incorrect `phi` calculation as a key issue.
[2025-04-15 16:47:56] - Fine-tuned `ThreeSunrise.svelte` parameters for color vibrancy and reduced haze. Rationale: Based on visual feedback indicating the previous effect was too muted/hazy. Adjusted <PERSON><PERSON> (0.2-0.6), <PERSON>rbid<PERSON> (6-10), <PERSON><PERSON>efficient (0.008), <PERSON>e Directional G (0.9), Fog Color (0x7f2f00 - deep orange), and Tone Mapping Exposure (0.8).# Decision Log

[2024-04-09 17:02:00] - Created Memory Bank structure to maintain project context

[2025-04-10 11:49:00] - Proposed replacing svelte-tabler with lucid-svelte for better build performance. Key factors:
- Current build process shows significant slowdown during icon transformations
- Created GitHub issue #17 to track the proposed change
- Benefits include faster builds, smaller bundle size, and tree-shaking support

10| [2025-04-10 21:22:00] - Decided to refactor DB initialization in hooks.server.ts to use a shared promise pattern.
11| - Rationale: Fixes performance regression caused by awaiting `getDb()` in `handle` on every request, while still making the DB instance available via `locals`. The connection is initiated once at server startup (outside build), and the promise is awaited in the hook.
12| - Implemented in `src/hooks.server.ts` and updated `src/app.d.ts`.
[2025-04-17 18:26:15] - Decided to create a CSS-based 3D parallax background effect (`CssParallaxBackground.svelte`) as an alternative to the Three.js version (`ThreeSunrise.svelte`). Rationale: Aiming for potentially better performance and simpler implementation/maintenance while still providing a subtle, premium background animation for the Hero component.