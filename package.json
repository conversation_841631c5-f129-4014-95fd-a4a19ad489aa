{"name": "arisetransit", "version": "0.0.1", "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-vercel": "^5.6.3", "@sveltejs/enhanced-img": "^0.4.4", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "daisyui": "^5.0.0", "drizzle-kit": "^0.30.6", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.3.3", "formsnap": "^2.0.0", "globals": "^15.14.0", "jsdom": "^25.0.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-meta-tags": "^4.2.0", "sveltekit-superforms": "^2.24.0", "tailwindcss": "^4.0.9", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.0.0", "vite-plugin-wasm": "^3.3.0", "vitest": "^3.0.0", "zod": "^3.24.2"}, "private": true, "scripts": {"dev": "vite dev --host 0.0.0.0", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint \"src/**/*.{ts,svelte}\" --ext .ts,.svelte", "test:unit": "vitest", "test": "npm run test:unit -- --run", "db:push": "node -r dotenv/config ./node_modules/.bin/drizzle-kit push"}, "type": "module", "dependencies": {"@ethercorps/sveltekit-og": "^3.0.0", "@fontsource-variable/playfair-display": "^5.2.5", "@fontsource/poppins": "^5.2.5", "@resvg/resvg-js": "^2.6.2", "@sentry/sveltekit": "^9", "@types/lodash-es": "^4.17.12", "@types/three": "^0.175.0", "@vercel/og": "^0.6.8", "dotenv": "^16.4.7", "drizzle-orm": "^0.41.0", "install": "^0.13.0", "lodash-es": "^4.17.21", "lucide-svelte": "^0.483.0", "mcp-svelte-docs": "^0.0.11", "postgres": "^3.4.5", "posthog-js": "^1.235.4", "posthog-node": "^4.11.3", "tailwind-scrollbar": "^4.0.1", "three": "^0.175.0"}}