# Viewport Usage Guide

This document explains how to use the viewport utility in our Svelte application for responsive design.

## Overview

Our application uses a modern viewport utility built with Svelte 5 runes that provides:

- Reactive viewport dimensions (width, height)
- Breakpoint detection matching Tailwind/daisyUI (sm, md, lg, xl, 2xl)
- Device category helpers (isMobile, isTablet, isDesktop)
- Media query matching
- SSR-safe implementation with sensible defaults

## Basic Usage

The viewport utility can be imported directly from `$lib/utils/viewport.svelte.ts`:

```svelte
<script>
	import { isMobile, isTablet, isDesktop } from '$lib/utils/viewport.svelte';
	// Direct store values
	const showMobileView = $isMobile;
</script>

{#if showMobileView}
	<div>Mobile-specific content</div>
{/if}
```

## Implementation Methods

There are several ways to use the viewport utility in components. Choose the appropriate method based on your specific needs:

### 1. Direct Import Method

**Best for:** Simple components, standalone UI elements

```svelte
<script>
	import { isMobile, isDesktop, viewport } from '$lib/utils/viewport.svelte';
	// direct store values
	const buttonClass = $isMobile
	  ? 'btn-sm'
	  : $isDesktop
	    ? 'btn-lg'
	    : 'btn-md';

	// Or use viewport utility functions
	const showExtraContent = $derived(viewport.atLeast('lg')());
</script>
```

### 3. Responsive Classes Pattern

**Best for:** Styling-focused components, Tailwind integration

```svelte
<script>
	import { isMobile, isTablet, isDesktop } from '$lib/utils/viewport.svelte';

	// Direct store usage
	const containerClass = $isMobile
	  ? 'p-4 gap-2'
	  : $isTablet
	    ? 'p-6 gap-4'
	    : 'p-8 gap-6';
</script>

<div class={`flex flex-col ${containerClass}`}>
	<!-- Content -->
</div>
```

### 4. Conditional Rendering Pattern

**Best for:** Different UI layouts per breakpoint

```svelte
<script>
	import { atLeast, between, below } from '$lib/utils/viewport.svelte';

	// Direct derived stores
	const mobileOnly = $below('md');
	const tabletOnly = $between('md', 'lg');
	const desktopOnly = $atLeast('lg');
</script>

{#if mobileOnly}
	<MobileLayout />
{:else if tabletOnly}
	<TabletLayout />
{:else}
	<DesktopLayout />
{/if}
```

### 5. Active Breakpoint Method

**Best for:** Fine-grained control, value mapping

```svelte
<script>
	import { activeBreakpoint } from '$lib/utils/viewport.svelte';

	// Direct store usage
	const currentBreakpoint = $activeBreakpoint;
</script>
```

## When to Use Each Method

| Method                | Best For                     | Why                                        |
| --------------------- | ---------------------------- | ------------------------------------------ |
| Direct Import         | Simple components            | Easiest implementation, self-contained     |
| Context               | Reusable libraries           | Flexible, respects parent context          |
| Responsive Classes    | Styling changes              | Works well with utility CSS, clear styling |
| Conditional Rendering | Different UIs per breakpoint | Clear separation, performance benefits     |
| Active Breakpoint     | Precise control              | Fine-grained breakpoint awareness          |

## Available Breakpoints

Our application uses the following breakpoints, matching Tailwind/daisyUI:

| Breakpoint | Width (px) | Description                           |
| ---------- | ---------- | ------------------------------------- |
| xs         | < 640      | Extra small devices (not in Tailwind) |
| sm         | ≥ 640      | Small devices (phones)                |
| md         | ≥ 768      | Medium devices (tablets)              |
| lg         | ≥ 1024     | Large devices (desktops)              |
| xl         | ≥ 1280     | Extra large devices (large desktops)  |
| 2xl        | ≥ 1536     | 2X-Large devices (larger desktops)    |

## Utility Functions

The viewport utility provides several helper functions:

- `atLeast(breakpoint)`: Check if viewport is at least the specified breakpoint
- `below(breakpoint)`: Check if viewport is below the specified breakpoint
- `between(min, max)`: Check if viewport is between the specified breakpoints
- `matchesMediaQuery(query)`: Check if a media query matches

## Best Practices

1. **Always call viewport functions** - Values are reactive when called as functions: `isMobile()` not `isMobile`
2. **Use $derived for reactivity** - Wrap derived values in `$derived` to maintain reactivity
3. **Mobile-first approach** - Design for mobile first, then enhance for larger screens
4. **Combine with Tailwind** - Use Tailwind's responsive utilities alongside viewport helpers
5. **SSR considerations** - The utility provides sensible defaults for SSR

## Example Component

Here's a complete example of a responsive component:

```svelte
<script>
	import { isMobile, isTablet, isDesktop } from '$lib/utils/viewport.svelte';

	// Responsive properties
	const cardClass = $derived(isMobile() ? 'card-compact' : isTablet() ? 'card-normal' : 'card-lg');

	const showExtendedInfo = $derived(isDesktop());
</script>

<div class={`card ${cardClass}`}>
	<div class="card-body">
		<h2 class="card-title">Responsive Card</h2>
		<p>This card adapts to different screen sizes.</p>

		{#if showExtendedInfo}
			<div class="extended-info">
				<p>Additional information only shown on desktop</p>
			</div>
		{/if}

		<div class="card-actions justify-end">
			<button class="btn btn-primary">Action</button>
		</div>
	</div>
</div>
```
