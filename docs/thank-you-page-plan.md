# Plan: Implement Contact Form Thank You Page

Based on the research in `.spec-prompts/thank-you-page-update.md`, this plan outlines the steps to add a dedicated thank-you page that appears after successful contact form submission.

## Objectives

-   Provide clear confirmation to the user upon form submission.
-   Enhance user experience by setting expectations (e.g., response time).
-   Establish the foundation for conversion tracking (e.g., Google Ads).
-   Redirect users from the contact form to the thank-you page upon success.

## Implementation Steps

1.  **Create Thank You Page Route & Component:**
    *   **Action:** Create a new SvelteKit route at `/contact/thank-you`.
    *   **Files:**
        *   Create `src/routes/contact/thank-you/+page.svelte`.
    *   **Responsibility:** `code` mode.

2.  **Design Thank You Page Content:**
    *   **Action:** Implement the UI within `src/routes/contact/thank-you/+page.svelte`.
    *   **Content Requirements (based on research):**
        *   **Confirmation Message:** Clear text confirming receipt (e.g., "Thank you for contacting us! Your message has been received.").
        *   **Next Steps:** Information on what happens next (e.g., "We appreciate your interest and will respond as soon as possible," or "We aim to reply within 24 hours.").
        *   **Optional CTAs:** Consider adding links to relevant sections like FAQ or a newsletter signup if applicable to the site.
    *   **Responsibility:** `code` mode.

3.  **Implement Server-Side Redirect:**
    *   **Action:** Modify the contact form submission logic to redirect upon success.
    *   **File:** Likely `src/routes/contact/+page.server.ts` (verify this handles the form `POST` request).
    *   **Logic:** Inside the form action handler, after successfully processing the form data (e.g., saving to DB, sending email), use SvelteKit's `redirect` function:
        ```typescript
        import { redirect } from '@sveltejs/kit';
        // ... other imports and form logic ...

        export const actions = {
          default: async ({ request }) => {
            // ... process form data ...

            // If successful:
            throw redirect(303, '/contact/thank-you'); // Use 303 See Other for POST redirects

            // Handle errors otherwise
          }
        };
        ```
    *   **Responsibility:** `code` mode.

4.  **Add Conversion Tracking (Future Task):**
    *   **Action:** Integrate tracking scripts (e.g., Google Tag Manager, Google Ads conversion tag) into the thank-you page.
    *   **File:** `src/routes/contact/thank-you/+page.svelte`.
    *   **Details:** This involves adding the necessary JavaScript snippets, potentially within a `<svelte:head>` tag or using a dedicated integration component/utility. Ensure it fires only when the page loads after a successful submission.
    *   **Responsibility:** `code` or `devops` mode (depending on complexity/infrastructure).
    *   **Note:** This should be a separate, subsequent task.

5.  **Implement Confirmation Email (Future Task):**
    *   **Action:** Add logic to send a confirmation email to the user after successful form submission.
    *   **File:** `src/routes/contact/+page.server.ts` (within the form action).
    *   **Details:** Requires integrating an email sending service (e.g., Resend, SendGrid) and crafting the email content.
    *   **Responsibility:** `code` mode.
    *   **Note:** This should be a separate, subsequent task.

6.  **Testing:**
    *   **Action:** Thoroughly test the entire flow.
    *   **Steps:**
        *   Submit the contact form with valid data.
        *   Verify successful redirection to `/contact/thank-you`.
        *   Verify the thank-you page displays the correct content.
        *   Submit the contact form with invalid data.
        *   Verify appropriate error messages are shown and no redirect occurs.
        *   (Future) Test conversion tracking fires correctly.
        *   (Future) Test confirmation email is received.
    *   **Responsibility:** `tdd` or `code` mode.

## File Structure Summary

-   **New File:** `src/routes/contact/thank-you/+page.svelte`
-   **Modified File:** `src/routes/contact/+page.server.ts`
-   **New Document:** `docs/thank-you-page-plan.md` (This document)

## Next Steps

1.  Create this plan document (`docs/thank-you-page-plan.md`).
2.  Delegate the implementation tasks (Steps 1-3, 6) to the appropriate modes (`code`, `tdd`).
3.  Create separate tasks for future enhancements (Steps 4, 5).