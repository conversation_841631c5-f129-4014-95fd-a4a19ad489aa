# SEO and Meta Tag Improvements Guide

This document outlines necessary improvements to the website's SEO and meta tags, explaining the purpose and importance of each change.

## 1. `src/routes/+layout.svelte` Changes

This file serves as the main layout for the application's pages. The following adjustments are needed within the `<svelte:head>` section:

### Redundant `<meta name="viewport" ...>` Tag

*   **What:** The `<meta name="viewport" content="width=device-width, initial-scale=1">` tag is currently present in the layout.
*   **Why:** In many SvelteKit projects, the viewport meta tag is already configured in the root `src/app.html` file. Including it again in the layout can be redundant and potentially lead to unexpected behavior if the definitions conflict.
*   **Action:** Remove the viewport meta tag from `+layout.svelte` if it's already defined correctly in `src/app.html`. Ensure `src/app.html` contains `%sveltekit.head%` where the layout's head content (including dynamically generated tags) will be injected.

### Missing Open Graph (OG) Tags

*   **What:** Tags like `og:title`, `og:description`, `og:type`, `og:url`, `og:image`, and `og:site_name` are missing.
*   **Why:** Open Graph tags control how content appears when shared on social media platforms (like Facebook, Twitter, LinkedIn). They provide specific titles, descriptions, and images for link previews, enhancing visibility and click-through rates. Populating these dynamically based on the current page content ensures accurate and relevant previews.
*   **Action:** Add Open Graph meta tags within `<svelte:head>`. These tags should be populated dynamically using data loaded in the corresponding `+layout.server.ts` or `+page.server.ts` / `+page.ts` files.

    ```html
    <!-- Example in +layout.svelte -->
    <meta property="og:title" content={data.title || 'Default Title'} />
    <meta property="og:description" content={data.description || 'Default description.'} />
    <meta property="og:type" content="website" />
    <meta property="og:url" content={data.url} />
    <meta property="og:image" content={data.ogImage || '/default-og-image.png'} />
    <meta property="og:site_name" content="Your Site Name" />
    ```

### Missing Canonical Link Tag

*   **What:** The `<link rel="canonical" href="...">` tag is missing.
*   **Why:** A canonical tag tells search engines the "preferred" version of a URL when multiple URLs might display similar or identical content (e.g., with/without trailing slashes, query parameters). This prevents duplicate content issues, consolidating ranking signals to the correct page.
*   **Action:** Add a canonical link tag, dynamically setting the `href` attribute to the current page's absolute URL, which should be provided by the server-side load function.

    ```html
    <!-- Example in +layout.svelte -->
    <link rel="canonical" href={data.url} />
    ```

### Missing Robots Meta Tag

*   **What:** The `<meta name="robots" content="...">` tag is missing.
*   **Why:** This tag provides instructions to search engine crawlers (like Googlebot) on how to crawl and index the page's content. `index, follow` is a common default, telling bots to index the page and follow links on it. While often the default behavior if omitted, explicitly setting it ensures clarity. You might change this on specific pages (e.g., `noindex` for private areas).
*   **Action:** Add the robots meta tag. For most public pages, `index, follow` is appropriate.

    ```html
    <!-- Example in +layout.svelte -->
    <meta name="robots" content="index, follow" />
    ```

## 2. `src/routes/+layout.server.ts` Changes

This server-side load function runs before the layout is rendered, allowing data fetching and preparation.

### Missing Data Fields in `load` Function

*   **What:** The `load` function currently doesn't return essential data fields needed for the meta tags: `title`, `description`, `url`, and `ogImage`.
*   **Why:** To populate the meta tags (like `<title>`, `og:title`, `og:description`, `og:image`, `canonical`) dynamically in `+layout.svelte`, the data must be provided by a `load` function. The layout component receives this data via its `data` prop.
*   **Action:** Modify the `load` function to return an object containing these fields. The `url` should be constructed from the `event.url.href` property to ensure it's the correct, absolute URL for the current request. A default `ogImage` URL should also be provided. Page-specific `load` functions (`+page.server.ts` or `+page.ts`) can override the layout's defaults for `title` and `description`.

    ```typescript
    // Example in +layout.server.ts
    import type { LayoutServerLoad } from './$types';

    export const load: LayoutServerLoad = async (event) => {
        const defaultTitle = 'Arise Transit - Premier DFW Chauffeur Service';
        const defaultDescription = 'Experience luxury and reliability with Arise Transit, offering top-tier black car and SUV chauffeur services in the Dallas-Fort Worth area.';
        const defaultOgImage = new URL('/static/favicon.png', event.url).href; // Construct absolute URL

        return {
            // Props for the layout component
            title: defaultTitle,
            description: defaultDescription,
            url: event.url.href, // Use the current absolute URL for canonical and OG
            ogImage: defaultOgImage,
            // Include other data needed by the layout/page...
            pathname: event.url.pathname // Often useful for navigation state
        };
    };
    ```

## 3. (Optional Bonus) JSON-LD Structured Data

*   **What:** JSON-LD (JavaScript Object Notation for Linked Data) is a method of encoding structured data on a webpage using JSON format, typically placed within a `<script type="application/ld+json">` tag in the `<head>`.
*   **Why:** Structured data helps search engines understand the content and context of your page more effectively. This can lead to enhanced search result features (like rich snippets, knowledge graph panels), improving visibility and providing users with more information directly in search results.
*   **Action:** Consider adding `WebSite` schema markup to your main layout (`+layout.svelte`) to define basic information about your site for search engines. More specific schema (like `Organization`, `Service`, `Article`, `FAQPage`) can be added to relevant pages.

    ```html
    <!-- Example WebSite Schema in +layout.svelte -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "{data.url}", // Use the canonical URL
        "name": "Arise Transit",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://www.arisetransit.com/search?q={search_term_string}", // Example search URL
          "query-input": "required name=search_term_string"
        }
      }
    </script>
    ```

By implementing these changes, the website's visibility on search engines and social media platforms can be significantly improved.