import { loadEnv } from 'vite';

// Load environment variables from .env files
const env = loadEnv('', process.cwd(), '');

if (!env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}

export default {
  schema: './src/lib/server/db/schema.ts',
  out: './drizzle', // Directory to store migration files (optional for push)
  dialect: 'postgresql', // Use 'dialect' instead of 'driver'
  dbCredentials: {
    url: env.DATABASE_URL, // Use the loaded environment variable
  },
  verbose: true, // Enable verbose logging
  strict: true, // Enable strict mode
};
