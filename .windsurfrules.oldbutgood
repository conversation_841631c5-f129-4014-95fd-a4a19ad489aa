```markdown
# The SvelteKit + Svelte 5 + daisyUI Ruleset: A Beautifully Unified Approach

This ruleset provides a definitive guide for building elegant and efficient web applications using the powerful combination of SvelteKit, Svelte 5 (with runes), and daisyUI. It emphasizes a clean separation of concerns, leveraging Svelte for logic and interactivity, and daisy<PERSON> for visual styling.

## I. Core Principles: Svelte-First, Style with daisyUI

1.  **Svelte Core:** Svelte 5 runes (`$state`, `$derived`, `$effect`, `$props`, `$bindable`) are the **primary and exclusive** mechanism for reactivity, state management, and component logic. Embrace runes fully, avoiding legacy Svelte 3/4 patterns.
2.  **Styling with daisyUI:** daisyUI is dedicated solely to visual styling via its class-based system. Apply daisyUI component classes and Tailwind utilities within Svelte components.  Minimize custom CSS as much as possible.
3.  **Component Architecture:** Build applications as hierarchies of reusable Svelte components. Encapsulate logic and structure within components, and style them with daisyUI classes.
4.  **Filesystem Routing (SvelteKit):**  Utilize SvelteKit's intuitive filesystem-based routing for page and API structure within `src/routes`.
5.  **Server-Side Rendering (SSR) by <PERSON><PERSON><PERSON> (SvelteKit):** Understand and leverage SvelteKit's SSR capabilities for performance and SEO. Differentiate between universal (`+page.js`) and server-only (`+page.server.js`) `load` functions for data fetching.
6.  **Progressive Enhancement:**  Prioritize a functional baseline experience without JavaScript, then enhance with Svelte's reactivity for a richer user experience.
7.  **Accessibility First:**  Build accessible applications using semantic HTML, ARIA attributes where necessary, and rigorous testing with accessibility tools.
8.  **Testing & Quality:** Implement comprehensive testing strategies (Unit, Component, E2E) to ensure application robustness and maintainability.
9.  **Type Safety with TypeScript:**  Employ TypeScript throughout your project for improved code maintainability, reduced errors, and enhanced developer experience.

## II. Project Structure: Standard SvelteKit Layout

Adhere to the established SvelteKit project structure for organization and maintainability:

```
my-project/
├── src/
│   ├── lib/          # Reusable components, utilities, shared logic ($lib alias)
│   │   ├── server/     # Server-only code ($lib/server alias)
│   │   └── ...
│   ├── params/       # Custom route param matchers
│   ├── routes/       # Pages, layouts, and API endpoints (filesystem routing)
│   │   ├── +page.svelte        # Page component
│   │   ├── +page.js/.ts        # Universal load function
│   │   ├── +page.server.js/.ts # Server-only load function
│   │   ├── +layout.svelte      # Layout component
│   │   ├── +layout.js/.ts      # Universal layout load function
│   │   ├── +layout.server.js/.ts# Server-only layout load function
│   │   ├── +error.svelte       # Custom error page
│   │   └── +server.js/.ts      # API endpoint handlers
│   ├── app.html      # Root HTML template
│   ├── error.html    # Fallback error page
│   ├── hooks.client.js/.ts # Client-side hooks
│   ├── hooks.server.js/.ts # Server-side hooks
│   └── service-worker.js/.ts # Optional service worker
├── static/         # Static assets (images, fonts, etc.)
├── package.json
├── svelte.config.js
├── tsconfig.json / jsconfig.json
└── vite.config.js/.ts
```

## III. Svelte 5 Component Syntax: Runes as the Foundation

Master and exclusively use Svelte 5's rune-based syntax for component development:

### 1. Reactivity with Runes: The Core

*   **`$state(initialValue)`:** Declare reactive variables.  **Directly mutate** these variables to trigger updates.

    ```svelte
    Syntax: Declare a reactive count variable, initialized to 0.  Changes to `count` will automatically update the UI where it's used.

    ```svelte
    <script>
        let count = $state(0);
        function increment() { count++; }
    </script>
    <button onclick={increment}>Count: {count}</button>
    ```

*   **`$derived(expression)`:** Create computed, reactive values.  **Avoid side-effects** within `$derived` expressions.

    ```svelte
    Syntax: Calculate a derived value `doubled` that is always twice the value of `count`. `doubled` updates whenever `count` changes.

    ```svelte
    <script>
        let count = $state(0);
        let doubled = $derived(count * 2);
    </script>
    <p>Count: {count}, Doubled: {doubled}</p>
    ```

*   **`$effect(() => { sideEffectCode })`:**  Execute side-effects (DOM manipulation, timers, logging) **after** DOM updates. Svelte automatically tracks dependencies within `$effect` for efficient updates.

    ```svelte
    Syntax:  Run code after each update where `count` changes. In this case, log the new `count` to the console.  Svelte efficiently tracks `count` as a dependency.

    ```svelte
    <script>
        let count = $state(0);
        $effect(() => { console.log('Count:', count); });
    </script>
    ```

*   **`$effect.pre(() => { preDOMSideEffectCode })`:** Execute side-effects **before** DOM updates (e.g., DOM measurements). Like `$effect`, dependency tracking is automatic.

    ```svelte
    Syntax:  Run code before the DOM updates. Useful for getting element dimensions before changes are rendered, like scrolling to the bottom of a div after new messages are added. Svelte efficiently tracks dependencies.

    ```svelte
    <script>
        let messages = $state([]);
        let div;
        $effect.pre(() => { if (div) { /* ... measure DOM ... */ } });
    </script>
    <div bind:this={div}>...</div>
    ```

*   **`$props()`:** Declare component properties. Use destructuring for clarity and default values.

    ```svelte
    Syntax: Define props `name` (default "Guest") and `count` (bindable, default 0) for a component.

    ```svelte
    <script>
        let { name = "Guest", count = $bindable(0) } = $props();
    </script>
    <h1>Hello, {name}! Count: {count}</h1>
    ```

*   **`$bindable(initialValue)`:**  Create a two-way bindable prop within `$props()`.

### 2. Event Handling: Attributes, Not Directives

Use standard event attributes (e.g., `onclick`, `oninput`) for event handlers. Svelte utilizes **event delegation** for optimized performance.

```svelte
Syntax:  Call the `handleClick` function when the button is clicked.

```svelte
<script>
    function handleClick() { /* ... */ }
</script>
<button onclick={handleClick}>Click</button>
```

### 3. Snippets: Modern Content Projection (Replacing Slots)

Use snippets for content projection, offering more flexibility than traditional slots and improved type safety. Snippets provide a more powerful and flexible content projection mechanism compared to slots.

```svelte
Syntax:  Parent component passes content to the Child component using snippets named `header` and `default`. The Child component then renders these snippets in designated areas. Snippets offer more flexible and reusable content projection compared to slots.

```svelte
<!--- Parent Component --->
<Child>
    {#snippet header()}<h1>Custom Header</h1>{/snippet}
    {#snippet default()}<p>Default Content</p>{/snippet}
</Child>

<!--- Child Component --->
<script> let { children, header } = $props(); </script>
{@render header?.()}
<div>{@render children?.()}</div>
```

### 4.  `{@const expression}`: Local Constants

Declare block-scoped constants using `{@const}`.

```svelte
Syntax:  Inside an `#each` block, calculate the `area` for each `item` and make it available as a constant within that iteration.

```svelte
{#each items as item}
    {@const area = item.width * item.height}
    <p>Area: {area}</p>
{/each}
```

### 5.  `{@html markup}`: Raw HTML (Use Sparingly!)

Render raw HTML using `{@html}`.  Use with extreme caution and **only with trusted content** to prevent XSS vulnerabilities.

### 6.  `{@debug variables}`: Development Debugging

Use `{@debug}` to log variable values to the console whenever they change during development.

### 7.  `bind:property={variable}`: Two-Way Binding

Establish two-way data binding using `bind:`. For custom logic, use function bindings: `bind:property={{ get, set }}`.

```svelte
Syntax:  Bind the `value` of the input field to the `name` variable.  Changes in the input will update `name`, and vice versa.

```svelte
<script> let name = $state(''); </script>
<input bind:value={name} /> <p>Hello, {name}!</p>
```

### 8.  `use:action` : Actions for DOM Interactions

Apply actions to elements using `use:`. Actions are functions that run when an element is created and can return an object with `update` and `destroy` methods.

### 9. Transitions & Animations: `transition:`, `in:`, `out:`, `animate:`

Utilize transition directives for smooth visual effects. Prefer built-in transitions from `svelte/transition`.

### 10. `style:property="value"`: Dynamic Inline Styles

Set inline styles dynamically with `style:`.

### 11. `class={conditionalClasses}`: Conditional CSS Classes

Apply CSS classes conditionally using the `class` attribute with objects or arrays.

```svelte
Syntax: Apply the class 'active-class' only when `isActive` is true, and always apply 'other-class'.

```svelte
<div class={{ 'active-class': isActive, 'other-class': true }}></div>
```

### 12. Control Flow: `{#if}`, `{#each}`, `{#await}`, `{#key}`

Use Svelte's built-in control flow blocks for conditional rendering, lists, asynchronous operations, and keyed components.

### 13. `<svelte:head>`: Document Head Management

Inject elements into the `<head>` of the document using `<svelte:head>`.

### 14. `<svelte:element this={tagName}>`: Dynamic Elements

Render elements with tag names determined at runtime using `<svelte:element>`.

### 15. `<svelte:boundary>`: Error Boundaries

Create error boundaries to gracefully handle errors within components and prevent cascading failures.

## IV. SvelteKit Specifics: Routing, Data, and Hooks

Master SvelteKit's core features for building robust applications:

1.  **Filesystem Routing:**  Organize routes within `src/routes`. Use `+page.svelte`, `+layout.svelte`, `+page.js`, `+page.server.js`, `+server.js`, and `+error.svelte` for route definitions and logic. Employ route groups `(group)` for organization and `@` for layout breakouts.

2.  **Data Loading (`load` functions):**
    *   **`+page.js`, `+layout.js` (Universal):** Fetch data that can be safely shared with the client.
    *   **`+page.server.js`, `+layout.server.js` (Server-only):**  Fetch sensitive data (database access, API keys).
    *   Use `event.fetch` for requests within `load` functions.
    *   `await parent()` to access data from parent layouts.
    *   `setHeaders` for HTTP header manipulation and **controlling caching behavior**.
    *   `depends` and `untrack` for load function dependency management.

3.  **Form Actions (`+page.server.js`):** Define form handling logic within `+page.server.js` using `export const actions = { ... }`. Use `method="POST"` in forms and `use:enhance` for progressive enhancement. Handle validation with `fail` and redirects with `redirect`. **Leverage server-side form validation for enhanced security.**

4.  **Hooks (`src/hooks.server.js`, `src/hooks.client.js`, `src/hooks.js`):**
    *   **Server Hooks (`hooks.server.js`):** `handle` (request/response interception), `handleFetch` (fetch modification), `handleError` (server-side error handling), `init` (server initialization). **Ensure proper ordering of `handle` hooks when using `sequence`.**
    *   **Client Hooks (`hooks.client.js`):** `handleError` (client-side error handling), `init` (client initialization).
    *   **Universal Hooks (`hooks.js`):** `reroute` (URL-to-route mapping), `transport` (customizing request transport).

5.  **Environment Variables:** Access environment variables using `$env/static/private`, `$env/static/public`, `$env/dynamic/private`, and `$env/dynamic/public`. Understand the distinction between build-time and runtime, private and public variables.

6.  **App State (`$app/state`):** Utilize the reactive `page` object from `$app/state` for accessing current page information: `page.url`, `page.params`, `page.route.id`, `page.status`, `page.error`, `page.data`, `page.form`, `page.state`.

7.  **App Navigation (`$app/navigation`):** Use functions from `$app/navigation` for programmatic navigation and control: `goto`, `invalidate`, `invalidateAll`, `preloadData`, `preloadCode`, `beforeNavigate`, `afterNavigate`, `onNavigate`, `disableScrollHandling`, `pushState`, `replaceState`.

8.  **Service Workers (`src/service-worker.js`):** Implement service workers for offline capabilities and performance enhancements using the `$service-worker` module.

## V. daisyUI Integration: Styling with Classes

Apply daisyUI's class-based styling system within your Svelte components for rapid and consistent UI development:

1.  **Installation:** Install daisyUI as a Tailwind CSS plugin and include it in your main CSS file.

    ```bash
    npm i -D daisyui@latest
    ```

    ```css
    /* main.css */
    @import "tailwindcss";
    @plugin "daisyui";
    ```

2.  **Component Classes First:** Prioritize daisyUI component classes for styling. Refer to the comprehensive component documentation below for available classes and syntax.

3.  **Tailwind Utilities for Customization:** Use Tailwind CSS utility classes (e.g., `px-4`, `mt-2`, `text-lg`) for fine-tuning styles beyond daisyUI's defaults.

4.  **`!` Modifier (Use Sparingly):** Only use the `!` modifier (e.g., `bg-red-500!`) to override daisyUI styles when absolutely necessary due to CSS specificity issues. Aim to customize using daisyUI and Tailwind classes first.

5.  **Minimize Custom CSS:** Strive to achieve your desired styles using daisyUI and Tailwind classes. Minimize or eliminate the need for writing custom CSS.

6.  **Responsiveness:** Leverage Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`, `2xl:`) to create responsive layouts with daisyUI components.

7.  **Themes:** Configure daisyUI themes in your `tailwind.config.js` or CSS file to customize the visual appearance of your application. **Utilize the daisyUI theme generator for creating and customizing themes.**

8.  **Colors:** Utilize daisyUI's semantic color names (e.g., `primary`, `secondary`, `accent`, `base-100`, `neutral`) for a consistent and themeable color palette.

## VI. daisyUI Component Class Reference & Rules

This section provides a concise reference for each daisyUI component, including class names, syntax examples, and specific rules for usage, **including accessibility considerations**.

**(Component Class Reference - As Detailed in Original Document with Accessibility Rules Added)**

**(Accordion)**
*   **Class Names:** `collapse`, `collapse-title`, `collapse-content`, `collapse-arrow`, `collapse-plus`, `collapse-open`, `collapse-close`
*   **Syntax:**
    ```svelte
    Description: This code creates two accordion items. The first uses an arrow icon, the second a plus icon. Radio buttons are used to ensure only one item can be open at a time within the group.

    ```svelte
    <div class="collapse collapse-arrow">
      <input type="radio" name="my-accordion" />
      <div class="collapse-title">Accordion Item 1</div>
      <div class="collapse-content"><p>Content for item 1</p></div>
    </div>

    <div class="collapse collapse-plus">
      <input type="radio" name="my-accordion" />
      <div class="collapse-title">Accordion Item 2</div>
      <div class="collapse-content"><p>Content for item 2</p></div>
    </div>
    ```
*   **Rules:** Use radio inputs for accordion groups **(accessibility: ensures only one section is open at a time, improving navigation for screen reader users)**, `collapse-arrow`/`collapse-plus` for icons, `collapse-open`/`collapse-close` for state.

**(Alert)**
*   **Class Names:** `alert`, `alert-outline`, `alert-dash`, `alert-soft`, `alert-info`, `alert-success`, `alert-warning`, `alert-error`, `alert-vertical`, `alert-horizontal`
*   **Syntax:**
    ```svelte
    Description: These examples show basic alerts with different styles and colors. The first is a standard info alert, the second is a success alert with an outline style.

    ```svelte
    <div role="alert" class="alert alert-info">
      <span>This is an informational alert.</span>
    </div>

    <div role="alert" class="alert alert-success alert-outline">
      <span>Success!</span>
    </div>
    ```
*   **Rules:** `role="alert"` for accessibility **(accessibility: essential for screen readers to identify alert messages)**, combine style/color/direction classes, `sm:alert-horizontal` for responsiveness.

**(Avatar)**
*   **Class Names:** `avatar`, `avatar-group`, `avatar-online`, `avatar-offline`, `avatar-placeholder`
*   **Syntax:**
    ```svelte
    Description: The first example shows a single avatar with a rounded image. The second shows a group of avatars, with the last one as a placeholder displaying "+99".

    ```svelte
    <div class="avatar">
      <div class="w-16 rounded-full">
        <img src="/path/to/image.jpg" alt="Avatar" />
      </div>
    </div>

    <div class="avatar-group -space-x-6">
      <div class="avatar"><div class="w-12"><img src="/path/to/image1.jpg" alt="Avatar 1"/></div></div>
      <div class="avatar"><div class="w-12"><img src="/path/to/image2.jpg" alt="Avatar 2"/></div></div>
      <div class="avatar placeholder"><div class="w-12 bg-neutral text-neutral-content"><span>+99</span></div></div>
    </div>
    ```
*   **Rules:** `w-*`/`h-*` for size, `rounded-full` for circles, `avatar-group` for groups (negative `space-x-*` for overlap), `avatar-placeholder` for placeholders.

**(Badge)**
*   **Class Names:** `badge`, `badge-outline`, `badge-dash`, `badge-soft`, `badge-ghost`, `badge-neutral`, `badge-primary`, `badge-secondary`, `badge-accent`, `badge-info`, `badge-success`, `badge-warning`, `badge-error`, `badge-xs`, `badge-sm`, `badge-md`, `badge-lg`, `badge-xl`
*   **Syntax:**
    ```svelte
    Description: Examples of badges with different styles and colors. "New" is a primary badge, "Large" is an outline large badge, and "Error" is a ghost error badge.

    ```svelte
    <span class="badge badge-primary">New</span>
    <span class="badge badge-outline badge-lg">Large</span>
    <span class="badge badge-ghost badge-error">Error</span>
    ```
*   **Rules:** Inline usage, combine style/color/size classes.

**(Breadcrumbs)**
*   **Class Names:** `breadcrumbs`
*   **Syntax:**
    ```svelte
    Description: Creates a breadcrumb navigation. Each `<li>` represents a step in the navigation hierarchy, typically using `<a>` tags for links.

    ```svelte
    <div class="breadcrumbs">
      <ul>
        <li><a href="/">Home</a></li>
        <li><a href="/products">Products</a></li>
        <li>Product Details</li>
      </ul>
    </div>
    ```
*   **Rules:** `<a>` tags for links **(accessibility: ensures proper link semantics for screen readers)**, can contain icons.

**(Button)**
*   **Class Names:** `btn`, `btn-neutral`, `btn-primary`, `btn-secondary`, `btn-accent`, `btn-info`, `btn-success`, `btn-warning`, `btn-error`, `btn-outline`, `btn-dash`, `btn-soft`, `btn-ghost`, `btn-link`, `btn-active`, `btn-disabled`, `btn-xs`, `btn-sm`, `btn-md`, `btn-lg`, `btn-xl`, `btn-wide`, `btn-block`, `btn-square`, `btn-circle`
*   **Syntax:**
    ```svelte
    Description: Examples of different button styles. Includes primary, outline accent, ghost link, and a small square button with an icon (placeholder SVG).

    ```svelte
    <button class="btn btn-primary">Click Me</button>
    <button class="btn btn-outline btn-accent">Outline</button>
    <a class="btn btn-ghost" href="/somewhere">Link</a>
    <button class="btn btn-square btn-sm">
      <svg><!-- ... icon SVG ... --></svg>
    </button>
    ```
*   **Rules:** Use with `<button>`, `<a>`, `<input>` **(accessibility: semantically correct elements)**, combine classes, `btn-disabled` class + `tabindex="-1"` + `aria-disabled="true"` for accessibility **(accessibility: proper ARIA attributes for disabled state, avoid `disabled` HTML attribute when using classes for disabling)**, `btn-block` for full-width.

**(Calendar)**
*   **Class Names:** `cally`, `pika-single`, `react-day-picker` (and component specific classes)
*   **Syntax:**
    ```svelte
    Description: Shows syntax for using daisyUI with three different calendar libraries.  Specific usage depends on the chosen library's documentation.

    ```svelte
    For Cally:
    <calendar-date class="cally">{CONTENT}</calendar-date>

    For Pikaday:
    <input type="text" class="input pika-single">

    For React Day Picker:
    <DayPicker className="react-day-picker">
    ```
*   **Rules:** Supports Cally, Pikaday, React Day Picker – refer to their respective documentations for detailed usage **(accessibility: ensure chosen library is accessible, consider keyboard navigation and screen reader compatibility)**.

**(Card)**
*   **Class Names:** `card`, `card-title`, `card-body`, `card-actions`, `card-border`, `card-dash`, `card-soft`, `card-side`, `image-full`, `card-xs`, `card-sm`, `card-md`, `card-lg`, `card-xl`
*   **Syntax:**
    ```svelte
    Description: A card example displaying an image, title, description, and action buttons.  Common card structure with header, body, and actions sections.

    ```svelte
    <div class="card w-96 bg-base-100 shadow-xl">
      <figure><img src="/images/stock/photo-1606107557195-0e29a4b5b4aa.jpg" alt="Shoes" /></figure>
      <div class="card-body">
        <h2 class="card-title">Shoes!</h2>
        <p>If a dog chews shoes whose shoes does he choose?</p>
        <div class="card-actions justify-end">
          <button class="btn btn-primary">Buy Now</button>
        </div>
      </div>
    </div>
    ```
*   **Rules:** `card-body` recommended, `card-title` for headings, `card-actions` for buttons, `image-full` for full image cover, `card-side` for horizontal layout, `sm:card-horizontal` for responsive.

**(Carousel)**
*   **Class Names:** `carousel`, `carousel-item`, `carousel-start`, `carousel-center`, `carousel-end`, `carousel-horizontal`, `carousel-vertical`
*   **Syntax:**
    ```svelte
    Description: A basic carousel with two images. Each image is wrapped in a `carousel-item` div.  The `w-full` class makes the carousel full-width.

    ```svelte
    <div class="carousel w-full">
      <div class="carousel-item w-full">
        <img src="/images/stock/photo-1559703248-dcaaec9fab78.jpg" class="w-full" alt="Tailwind CSS Carousel component" />
      </div>
      <div class="carousel-item w-full">
        <img src="/images/stock/photo-1565098772267-60af42b81ef2.jpg" class="w-full" alt="Tailwind CSS Carousel component" />
      </div>
    </div>
    ```
*   **Rules:** `carousel-item` for each item, `w-full` for full-width items, `carousel-start`/`carousel-center`/`carousel-end` for alignment.

**(Chat)**
*   **Class Names:** `chat`, `chat-image`, `chat-header`, `chat-footer`, `chat-bubble`, `chat-start`, `chat-end`, `chat-bubble-neutral`, `chat-bubble-primary`, etc.
*   **Syntax:**
    ```svelte
    Description: Example of a chat bubble from the "start" (left) side. Includes avatar, header with timestamp, bubble content, and footer.

    ```svelte
    <div class="chat chat-start">
      <div class="chat-image avatar">
        <div class="w-10 rounded-full">
          <img alt="Tailwind CSS chat bubble component" src="/images/stock/photo-1534528741775-53994a69daeb.jpg" />
        </div>
      </div>
      <div class="chat-header">
        Obi-Wan Kenobi
        <time class="text-xs opacity-50">12:45</time>
      </div>
      <div class="chat-bubble">You were the Chosen One!</div>
      <div class="chat-footer opacity-50">
        Delivered
      </div>
    </div>
    ```
*   **Rules:** `chat-start`/`chat-end` for positioning, `chat-bubble-*` for bubble styling.

**(Checkbox)**
*   **Class Names:** `checkbox`, `checkbox-primary`, `checkbox-secondary`, etc., `checkbox-xs`, `checkbox-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: Shows two checkboxes: a primary checkbox and a large accent checkbox that is checked by default.

    ```svelte
    <input type="checkbox" class="checkbox checkbox-primary" />
    <input type="checkbox" class="checkbox checkbox-accent checkbox-lg" checked />
    ```
*   **Rules:** Combine color and size classes.

**(Collapse)**
*   **Class Names:** `collapse`, `collapse-title`, `collapse-content`, `collapse-arrow`, `collapse-plus`, `collapse-open`, `collapse-close`
*   **Syntax:**
    ```svelte
    Description: A simple collapse component using a checkbox to toggle visibility. Clicking the title expands/collapses the content.

    ```svelte
    <div class="collapse collapse-arrow">
      <input type="checkbox" />
      <div class="collapse-title">
        Click me to show/hide content
      </div>
      <div class="collapse-content">
        <p>hello</p>
      </div>
    </div>
    ```
*   **Rules:** Checkbox input or `tabindex="0"` for accessibility **(accessibility: ensure keyboard access with `tabindex="0"` if not using checkbox)**, `collapse-arrow`/`collapse-plus` for icons.

**(Countdown)**
*   **Class Names:** `countdown`
*   **Syntax:**
    ```svelte
    Description:  A countdown timer displaying the number 10. The `--value` style needs to be dynamically updated with JavaScript to create a real countdown.

    ```svelte
    <span class="countdown">
      <span style="--value:10;"></span>
    </span>
    ```
*   **Rules:** Requires inline `--value` style, matching `<span>` content, JavaScript for updates, `aria-live="polite"` and `aria-label="{number}"` for accessibility **(accessibility: `aria-live` and `aria-label` are crucial for screen readers to announce countdown updates)**.

**(Diff)**
*   **Class Names:** `diff`, `diff-item-1`, `diff-item-2`, `diff-resizer`
*   **Syntax:**
    ```svelte
    Description: Creates a visual diff component showing two images side-by-side with a draggable resizer in between.

    ```svelte
    <figure class="diff aspect-video">
      <div class="diff-item-1">
        <img src="/images/stock/photo-1606107557195-0e29a4b5b4aa.jpg" alt="Image 1" />
      </div>
      <div class="diff-item-2">
        <img src="/images/stock/photo-1565098772267-60af42b81ef2.jpg" alt="Image 2" />
      </div>
      <div class="diff-resizer"></div>
    </figure>
    ```
*   **Rules:** `aspect-video` or other aspect ratio classes on `diff` container.

**(Divider)**
*   **Class Names:** `divider`, `divider-neutral`, `divider-primary`, etc., `divider-vertical`, `divider-horizontal`, `divider-start`, `divider-end`
*   **Syntax:**
    ```svelte
    Description: Examples of a divider with text label "OR" and a horizontal divider line.

    ```svelte
    <div class="divider">OR</div>
    <div class="divider divider-horizontal"></div>
    ```
*   **Rules:** `divider-horizontal` default, text content for labels.

**(Dock)**
*   **Class Names:** `dock`, `dock-item`, `dock-label`, `dock-active`, `dock-xs`, `dock-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: Creates a dock menu with two buttons. The "Profile" button is marked as active using `dock-active`.

    ```svelte
    <div class="dock">
      <button class="dock-item">
        <svg><!-- ... --></svg>
        <span class="dock-label">Home</span>
      </button>
      <button class="dock-item dock-active">
        <svg><!-- ... --></svg>
        <span class="dock-label">Profile</span>
      </button>
    </div>
    ```
*   **Rules:** `dock-active` for current item, `<meta name="viewport" content="viewport-fit=cover">` for iOS.

**(Drawer)**
*   **Class Names:** `drawer`, `drawer-toggle`, `drawer-content`, `drawer-side`, `drawer-overlay`, `drawer-end`, `drawer-open`
*   **Syntax:**
    ```svelte
    Description: A drawer layout with a main content area and a sidebar that slides in from the side when the "Open drawer" button is clicked. Uses a checkbox to control drawer state.

    ```svelte
    <div class="drawer">
      <input id="my-drawer" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content">
        <!-- Page content here -->
        <label for="my-drawer" class="btn btn-primary drawer-button">Open drawer</label>
      </div>
      <div class="drawer-side">
        <label for="my-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
        <ul class="menu p-4 w-80 min-h-full bg-base-200 text-base-content">
          <!-- Sidebar content here -->
          <li><a>Sidebar Item 1</a></li>
          <li><a>Sidebar Item 2</a></li>
        </ul>
      </div>
    </div>
    ```
*   **Rules:** Hidden `drawer-toggle` checkbox with unique `id` **(accessibility: ensure labels are properly associated with toggle for screen readers)**, `<label for="my-drawer">` to toggle, `drawer-content` for page, `drawer-side` for sidebar, `lg:drawer-open` for responsive sidebar.

**(Dropdown)**
*   **Class Names:** `dropdown`, `dropdown-content`, `dropdown-start`, `dropdown-center`, etc., `dropdown-hover`, `dropdown-open`
*   **Syntax:**
    ```svelte
    Description: A dropdown menu triggered by a button. Uses `tabindex="0"` for focus and accessibility. The dropdown content is a `<ul>` menu.

    ```svelte
    <!-- Using tabindex for focus -->
    <div class="dropdown">
      <div tabindex="0" role="button" class="btn m-1">Click</div>
      <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
        <li><a>Item 1</a></li>
        <li><a>Item 2</a></li>
      </ul>
    </div>
    ```
*   **Rules:** `tabindex="0"` and `role="button"` for accessibility **(accessibility: `tabindex` and `role="button"` are essential for keyboard navigation)**, `dropdown-content` can be any element, placement modifiers.

**(Fieldset)**
*   **Class Names:** `fieldset`, `fieldset-legend`, `fieldset-label`
*   **Syntax:**
    ```svelte
    Description:  Example of a fieldset component used to group form elements under a common legend and description.

    ```svelte
    <fieldset class="fieldset">
      <legend class="fieldset-legend">{title}</legend>
      {CONTENT}
      <p class="fieldset-label">{description}</p>
    </fieldset>
    ```
*   **Rules:** Any element as direct child for form elements **(accessibility: fieldset and legend improve form structure for screen readers)**.

**(File Input)**
*   **Class Names:** `file-input`, `file-input-ghost`, `file-input-neutral`, etc., `file-input-xs`, `file-input-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A bordered file input field with a maximum width of `max-w-xs`.

    ```svelte
    <input type="file" class="file-input file-input-bordered w-full max-w-xs" />
    ```
*   **Rules:** Combine style/color/size classes.

**(Filter)**
*   **Class Names:** `filter`, `filter-reset`
*   **Syntax:**
    ```svelte
    Description: Examples of filter components, one using a `<form>` and the other a `<div>`.  Includes radio buttons for filter options and a reset button.

    ```svelte
    <!-- Using a form -->
    <form class="filter">
      <input class="btn btn-square filter-reset" type="reset" value="×"/>
      <input class="btn" type="radio" name="filter-options" aria-label="Option 1" value="option1"/>
      <input class="btn" type="radio" name="filter-options" aria-label="Option 2" value="option2"/>
    </form>

    <!-- Without a form -->
    <div class="filter">
      <input class="btn filter-reset" type="radio" name="filter-options" aria-label="Reset"/>
      <input class="btn" type="radio" name="filter-options" aria-label="Option 1" value="option1"/>
      <input class="btn" type="radio" name="filter-options" aria-label="Option 2" value="option2"/>
    </div>
    ```
*   **Rules:** Radio inputs with same `name` **(accessibility: ensures proper grouping for assistive technologies)**, `filter-reset` button, prefer `<form>` **(accessibility: `<form>` provides semantic structure)**.

**(Footer)**
*   **Class Names:** `footer`, `footer-title`, `footer-center`, `footer-horizontal`, `footer-vertical`
*   **Syntax:**
    ```svelte
    Description: A footer example with two navigation sections ("Services" and "Company"). Uses `footer-title` for section headers and `link-hover` for hover effects on links.

    ```svelte
    <footer class="footer p-10 bg-base-200 text-base-content">
      <nav>
        <header class="footer-title">Services</header>
        <a class="link link-hover">Branding</a>
        <a class="link link-hover">Design</a>
      </nav>
      <nav>
        <header class="footer-title">Company</header>
        <a class="link link-hover">About us</a>
        <a class="link link-hover">Contact</a>
      </nav>
    </footer>
    ```
*   **Rules:** `footer-title` for headings, `sm:footer-horizontal` for responsive.

**(Hero)**
*   **Class Names:** `hero`, `hero-content`, `hero-overlay`
*   **Syntax:**
    ```svelte
    Description: A hero section with a background, centered content (heading, paragraph, button), using `hero-content` to structure the inner elements.

    ```svelte
    <div class="hero min-h-screen bg-base-200">
      <div class="hero-content text-center">
        <div class="max-w-md">
          <h1 class="text-5xl font-bold">Hello there</h1>
          <p class="py-6">Provident cupiditate voluptatem et in. Quaerat fugiat ut assumenda excepturi exercitationem quasi. In deleniti eaque aut repudiandae et a id nisi.</p>
          <button class="btn btn-primary">Get Started</button>
        </div>
      </div>
    </div>
    ```
*   **Rules:** `hero-content` for content, `hero-overlay` for overlays.

**(Indicator)**
*   **Class Names:** `indicator`, `indicator-item`, `indicator-start`, `indicator-center`, etc.
*   **Syntax:**
    ```svelte
    Description:  An indicator showing a badge "new" on top of a button labeled "Inbox". The badge is positioned using `indicator-item`.

    ```svelte
    <div class="indicator">
      <span class="indicator-item badge badge-secondary">new</span>
      <button class="btn">Inbox</button>
    </div>
    ```
*   **Rules:** `indicator-item` before content, placement classes for position.

**(Input)**
*   **Class Names:** `input`, `input-ghost`, `input-neutral`, etc., `input-xs`, `input-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A bordered text input field with placeholder text and a maximum width.

    ```svelte
    <input type="text" placeholder="Type here" class="input input-bordered w-full max-w-xs" />
    ```
*   **Rules:** Combine style/color/size classes.

**(Join)**
*   **Class Names:** `join`, `join-item`, `join-vertical`, `join-horizontal`
*   **Syntax:**
    ```svelte
    Description:  Vertically joined buttons. The `join` component groups buttons together visually without spacing between them.

    ```svelte
    <div class="join join-vertical">
      <button class="btn join-item">Button 1</button>
      <button class="btn join-item">Button 2</button>
      <button class="btn join-item">Button 3</button>
    </div>
    ```
*   **Rules:** `join-horizontal` default, `join-item` on elements within `join`.

**(Kbd)**
*   **Class Names:** `kbd`, `kbd-xs`, `kbd-sm`, etc.
*   **Syntax:**
    ```svelte
    Description:  Displays keyboard keys "Ctrl" and "C" using the `kbd` component.

    ```svelte
    <kbd class="kbd">Ctrl</kbd> + <kbd class="kbd">C</kbd>
    ```
*   **Rules:** Size classes for different sizes.

**(Label)**
*   **Class Names:** `label`, `floating-label`
*   **Syntax:**
    ```svelte
    Description: Examples of a regular label and a floating label. Regular labels are placed above the input, floating labels animate to become placeholders when input is empty.

    ```svelte
    <!-- Regular label -->
    <label class="input">
      <span class="label">Username</span>
      <input type="text" placeholder="username" class="input input-bordered" />
    </label>

    <!-- Floating label -->
    <label class="floating-label">
      <input type="text" placeholder="email" class="input input-bordered" />
      <span>Email</span>
    </label>
    ```
*   **Rules:** `input` class on parent for regular labels **(accessibility: improves label-input association)**, `floating-label` on parent for floating labels (input and label text in `<span>`).

**(Link)**
*   **Class Names:** `link`, `link-hover`, `link-neutral`, `link-primary`, etc.
*   **Syntax:**
    ```svelte
    Description: A primary colored link labeled "Learn more".

    ```svelte
    <a href="#" class="link link-primary">Learn more</a>
    ```
*   **Rules:** Underline styles for `<a>`, `link-hover` for hover effects.

**(List)**
*   **Class Names:** `list`, `list-row`, `list-col-wrap`, `list-col-grow`
*   **Syntax:**
    ```svelte
    Description: A list with two rows. Each `list-row` contains two spans, representing an item and its description.

    ```svelte
    <ul class="list">
      <li class="list-row">
        <span>Item 1</span>
        <span>Description 1</span>
      </li>
      <li class="list-row">
        <span>Item 2</span>
        <span>Description 2</span>
      </li>
    </ul>
    ```
*   **Rules:** `list-row` for items, second child fills space by default, `list-col-grow` for different child to fill space, `list-col-wrap` for wrapping.

**(Loading)**
*   **Class Names:** `loading`, `loading-spinner`, `loading-dots`, etc., `loading-xs`, `loading-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A large spinner loading indicator.

    ```svelte
    <span class="loading loading-spinner loading-lg"></span>
    ```
*   **Rules:** Combine style and size classes.

**(Mask)**
*   **Class Names:** `mask`, `mask-squircle`, `mask-heart`, etc., `mask-half-1`, `mask-half-2`
*   **Syntax:**
    ```svelte
    Description: An image masked with a "squircle" shape, with a fixed width and height.

    ```svelte
    <img class="mask mask-squircle w-24 h-24" src="/images/stock/photo-1559703248-dcaaec9fab78.jpg" alt="Masked Image" />
    ```
*   **Rules:** `w-*`/`h-*` for size, shape classes.

**(Menu)**
*   **Class Names:** `menu`, `menu-title`, `menu-dropdown`, `menu-dropdown-toggle`, `menu-disabled`, `menu-active`, `menu-focus`, `menu-dropdown-show`, `menu-xs`, `menu-sm`, etc., `menu-vertical`, `menu-horizontal`
*   **Syntax:**
    ```svelte
    Description: A vertical menu with regular items, a title section, and a submenu that expands on click.

    ```svelte
    <ul class="menu bg-base-100 w-56 rounded-box">
      <li><a>Item 1</a></li>
      <li><a>Item 2</a></li>
      <li class="menu-title">
        <span>Title</span>
      </li>
      <li>
        <details class="menu-dropdown">
          <summary class="menu-dropdown-toggle">Parent</summary>
          <ul>
            <li><a>Submenu 1</a></li>
            <li><a>Submenu 2</a></li>
          </ul>
        </details>
      </li>
    </ul>
    ```
*   **Rules:** `menu-horizontal` for horizontal menus, `lg:menu-horizontal` for responsive, `menu-title` for headings, `<details>`/`<summary>` for submenus **(accessibility: `<details>`/`<summary>` improve submenu accessibility)**, `menu-dropdown`/`menu-dropdown-toggle` for JS dropdowns.

**(Mockup Browser)**
*   **Class Names:** `mockup-browser`, `mockup-browser-toolbar`
*   **Syntax:**
    ```svelte
    Description: A mockup of a browser window, including a toolbar with an input field for a URL bar and a content area.

    ```svelte
    <div class="mockup-browser border bg-base-300">
      <div class="mockup-browser-toolbar">
        <input type="text" placeholder="Search or type URL" class="input input-bordered w-full" />
      </div>
      <div class="flex justify-center px-4 py-16 bg-base-200">Hello!</div>
    </div>
    ```
*   **Rules:** `<input class="input">` in `mockup-browser-toolbar` for URL bar.

**(Mockup Code)**
*   **Class Names:** `mockup-code`
*   **Syntax:**
    ```svelte
    Description: A mockup of a code block, showing lines with prefixes and code content.

    ```svelte
    <pre class="mockup-code">
      <code class="language-html">
        <template>
          <h1>Hello world!</h1>
        </template>
      </code>
    </pre>

    <pre class="mockup-code">
      <code class="language-js">
      const message = 'Hello world'
      console.log(message)
      </code>
    </pre>

    <div class="mockup-code">
    <pre data-prefix="$"><code>npm create svelte@latest my-app</code></pre>
    <pre data-prefix=">"><code>cd my-app</code></pre>
    <pre data-prefix="$"><code>npm install</code></pre>
    <pre data-prefix="$"><code>npm run dev</code></pre>
    </div>
    ```
*   **Rules:**  Use `<pre data-prefix="{prefix}">` to show a prefix before each line, `<pre data-prefix="> ">` for output lines.

**(Mockup Phone)**
*   **Class Names:** `mockup-phone`, `mockup-phone-camera`, `mockup-phone-display`
*   **Syntax:**
    ```svelte
    Description: A mockup of a phone, with a camera notch and a display area to embed phone screen content.

    ```svelte
    <div class="mockup-phone">
      <div class="mockup-phone-camera"></div>
      <div class="mockup-phone-display">
        <div class="artboard artboard-demo phone-1">
          <!-- Your content here -->
          <h1>Hello!</h1>
        </div>
      </div>
    </div>
    ```
*   **Rules:** `mockup-phone-camera` for notch, `mockup-phone-display` for screen content.

**(Mockup Window)**
*   **Class Names:** `mockup-window`
*   **Syntax:**
    ```svelte
    Description: A mockup of a window frame, useful for displaying content within a window-like container.

    ```svelte
    <div class="mockup-window bg-base-300">
      <div class="flex justify-center px-4 py-16 bg-base-200">
        <!-- Your content here -->
        <p>Window Content</p>
      </div>
    </div>
    ```
*   **Rules:** Basic window frame, add content inside.

**(Modal)**
*   **Class Names:** `modal`, `modal-box`, `modal-action`, `modal-backdrop`, `modal-toggle`, `modal-open`, `modal-top`, `modal-middle`, `modal-bottom`, `modal-start`, `modal-end`
*   **Syntax:**
    ```svelte
    Description:  Two ways to create modals. Method 1 (preferred) uses the HTML `<dialog>` element and JavaScript to show/close. Method 2 (legacy) uses a checkbox for toggling visibility.

    ```svelte
    <!-- Method 1: Using HTML dialog element (preferred) -->
    <button onclick="my_modal.showModal()">Open Modal</button>

    <dialog id="my_modal" class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg">Hello!</h3>
        <p class="py-4">Press ESC key or click outside to close</p>
        <div class="modal-action">
          <form method="dialog">
            <button class="btn">Close</button>
          </form>
        </div>
      </div>
      <form method="dialog" class="modal-backdrop">
          <button>close</button>
      </form>
    </dialog>

    <!-- Method 2: Using a checkbox (legacy) -->
    <input type="checkbox" id="my-modal-2" class="modal-toggle" />
    <div class="modal">
      <div class="modal-box">
        <h3 class="font-bold text-lg">Hello!</h3>
        <p class="py-4">Press ESC key or click the button below</p>
        <div class="modal-action">
          <label for="my-modal-2" class="btn">Close!</label>
        </div>
      </div>
      <label for="my-modal-2" class="modal-backdrop">Close</label>
    </div>
    ```
*   **Rules:** Method 1 (`<dialog>`) preferred (use `showModal()`, `<form method="dialog">`) **(accessibility: `<dialog>` is semantically correct for modals)**, Method 2 (checkbox) legacy, `modal-box` for content, `modal-action` for buttons, `modal-open` for CSS control, placement modifiers, `tabindex="0"` for focusability **(accessibility: improves keyboard navigation)**.

**(Navbar)**
*   **Class Names:** `navbar`, `navbar-start`, `navbar-center`, `navbar-end`
*   **Syntax:**
    ```svelte
    Description: A basic navbar with content sections aligned to the start, center, and end of the navbar.

    ```svelte
    <div class="navbar bg-base-100">
      <div class="navbar-start">
        <a class="btn btn-ghost text-xl">daisyUI</a>
      </div>
      <div class="navbar-center">
        <a class="btn btn-ghost text-xl">daisyUI</a>
      </div>
      <div class="navbar-end">
        <a class="btn btn-ghost text-xl">daisyUI</a>
      </div>
    </div>
    ```
*   **Rules:** `navbar-start`/`navbar-center`/`navbar-end` for horizontal positioning, use with `bg-base-*`.

**(Pagination)**
*   **Class Names:** `join`, `join-item` (uses `join` component)
*   **Syntax:**
    ```svelte
    Description: Pagination buttons using the `join` component to group them together. The button "2" is marked as active.

    ```svelte
    <div class="join">
      <button class="join-item btn">1</button>
      <button class="join-item btn btn-active">2</button>
      <button class="join-item btn">3</button>
      <button class="join-item btn">4</button>
    </div>
    ```
*   **Rules:** Uses `join` for grouping buttons, `join-item` on buttons, `btn` classes for styling.

**(Progress)**
*   **Class Names:** `progress`, `progress-neutral`, `progress-primary`, etc.
*   **Syntax:**
    ```svelte
    Description: Examples of progress bars with different colors, all set to different progress values using the `value` attribute.

    ```svelte
    <progress class="progress w-56" value="0" max="100"></progress>
    <progress class="progress progress-primary w-56" value="10" max="100"></progress>
    <progress class="progress progress-secondary w-56" value="40" max="100"></progress>
    <progress class="progress progress-accent w-56" value="70" max="100"></progress>
    <progress class="progress progress-info w-56" value="100" max="100"></progress>
    ```
*   **Rules:** `value` and `max` attributes required **(accessibility: `value` and `max` are essential for screen readers to understand progress)**, combine with color classes.

**(Radial Progress)**
*   **Class Names:** `radial-progress`
*   **Syntax:**
    ```svelte
    Description: A radial progress indicator showing 70% progress. The `--value` style controls the progress percentage.

    ```svelte
    <div class="radial-progress" style="--value:70;" role="progressbar">70%</div>
    ```
*   **Rules:** `--value` CSS variable for progress, `role="progressbar"` and `aria-valuenow="{value}"` for accessibility **(accessibility: `role="progressbar"` and `aria-valuenow` are essential for screen readers to interpret radial progress)**, `<div>` for text content, `--size`, `--thickness` for customization.

**(Radio)**
*   **Class Names:** `radio`, `radio-neutral`, `radio-primary`, etc., `radio-xs`, `radio-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: Two radio buttons, grouped by `name="radio-1"`. The first is a default radio, the second is primary colored. The first is checked by default.

    ```svelte
    <input type="radio" name="radio-1" class="radio" checked />
    <input type="radio" name="radio-1" class="radio radio-primary" />
    ```
*   **Rules:** `name` attribute for grouping **(accessibility: `name` attribute groups radio buttons for screen readers and keyboard navigation)**, combine color and size classes.

**(Range)**
*   **Class Names:** `range`, `range-neutral`, `range-primary`, etc., `range-xs`, `range-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A primary colored range input with a range from 0 to 100, initialized at 50.

    ```svelte
    <input type="range" min="0" max="100" value="50" class="range range-primary" />
    ```
*   **Rules:** `min` and `max` attributes required **(accessibility: `min` and `max` attributes define the range for assistive technologies)**, combine color and size classes.

**(Rating)**
*   **Class Names:** `rating`, `rating-half`, `rating-hidden`, `rating-xs`, `rating-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A 5-star rating component. Uses radio inputs and mask classes to create star icons. The second star is checked, indicating a 2-star rating.

    ```svelte
    <div class="rating">
      <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
      <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
      <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
      <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
      <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" />
    </div>
    ```
*   **Rules:** Radio inputs **(accessibility: radio inputs provide inherent accessibility for ratings)**, `rating-hidden` for clear option, `mask` classes for icons.

**(Select)**
*   **Class Names:** `select`, `select-ghost`, `select-neutral`, `select-primary`, etc., `select-xs`, `select-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A bordered select dropdown with options. The first option is disabled and selected by default as a placeholder.

    ```svelte
    <select class="select select-bordered w-full max-w-xs">
      <option disabled selected>Who shot first?</option>
      <option>Han Solo</option>
      <option>Greedo</option>
    </select>
    ```
*   **Rules:** Combine style/color/size classes.

**(Skeleton)**
*   **Class Names:** `skeleton`
*   **Syntax:**
    ```svelte
    Description: Two skeleton placeholders. One is a square of 32x32 units, the other is a full-width horizontal line of height 4.

    ```svelte
    <div class="skeleton w-32 h-32"></div>
    <div class="skeleton w-full h-4"></div>
    ```
*   **Rules:** `w-*`/`h-*` Tailwind classes for size.

**(Stack)**
*   **Class Names:** `stack`, `stack-top`, `stack-bottom`, `stack-start`, `stack-end`
*   **Syntax:**
    ```svelte
    Description:  Three divs stacked on top of each other, with different background colors and content, using the `stack` component.

    ```svelte
    <div class="stack">
      <div class="bg-primary text-primary-content w-40 h-40 rounded-lg shadow-lg">1</div>
      <div class="bg-secondary text-secondary-content w-40 h-40 rounded-lg shadow-lg">2</div>
      <div class="bg-accent text-accent-content w-40 h-40 rounded-lg shadow-lg">3</div>
    </div>
    ```
*   **Rules:** `w-*`/`h-*` Tailwind classes for stacked element size.

**(Stat)**
*   **Class Names:** `stats`, `stat`, `stat-title`, `stat-value`, `stat-desc`, `stat-figure`, `stat-actions`, `stats-horizontal`, `stats-vertical`
*   **Syntax:**
    ```svelte
    Description: A statistics component displaying "Downloads" stat. Includes an icon, title, value, and description.

    ```svelte
    <div class="stats shadow">
      <div class="stat">
        <div class="stat-figure text-secondary">
          <svg><!-- ... --></svg>
        </div>
        <div class="stat-title">Downloads</div>
        <div class="stat-value">31K</div>
        <div class="stat-desc">Jan 1st - Feb 1st</div>
      </div>
    </div>
    ```
*   **Rules:** `stats-horizontal` default, `stat-*` parts within `stat` elements.

**(Status)**
*   **Class Names:** `status`, `status-neutral`, `status-primary`, etc., `status-xs`, `status-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A primary status component.  This component is typically used to *style* other elements based on status, not display content itself.

    ```svelte
    <span class="status status-primary"></span>
    ```
*   **Rules:** Style other elements based on status (no visible rendering itself).

**(Steps)**
*   **Class Names:** `steps`, `step`, `step-icon`, `step-neutral`, `step-primary`, etc., `steps-vertical`, `steps-horizontal`
*   **Syntax:**
    ```svelte
    Description: A horizontal steps component showing a process with four steps. "Register" and "Choose plan" steps are marked as primary (completed/active).

    ```svelte
    <ul class="steps">
      <li class="step step-primary">Register</li>
      <li class="step step-primary">Choose plan</li>
      <li class="step">Purchase</li>
      <li class="step">Receive Product</li>
    </ul>
    ```
*   **Rules:** `steps-horizontal` default, `step-primary` for active step, `step-icon` for icons, `data-content` on `<li>` for data.

**(Swap)**
*   **Class Names:** `swap`, `swap-on`, `swap-off`, `swap-indeterminate`, `swap-active`, `swap-rotate`, `swap-flip`
*   **Syntax:**
    ```svelte
    Description: Two examples of swap components. The first uses a checkbox to toggle between smiley faces. The second uses the `swap-rotate` style and class-based activation for "ON/OFF" text.

    ```svelte
    <!-- Using a checkbox -->
    <label class="swap">
      <input type="checkbox" />
      <div class="swap-off">🙂</div>
      <div class="swap-on">😄</div>
    </label>

    <!-- Using a class name -->
    <div class="swap swap-rotate">
      <div class="swap-off">OFF</div>
      <div class="swap-on">ON</div>
    </div>
    ```
*   **Rules:** Checkbox or `swap-active` class control, `swap-on` for active, `swap-off` for inactive, `swap-indeterminate` for indeterminate checkbox.

**(Tab)**
*   **Class Names:** `tabs`, `tab`, `tab-content`, `tabs-box`, `tabs-border`, `tabs-lift`, `tab-active`, `tab-disabled`, `tabs-top`, `tabs-bottom`
*   **Syntax:**
    ```svelte
    Description:  Two tab examples. The first uses buttons for tabs, the second uses radio inputs with a "box" style for tabs.

    ```svelte
    <!-- Using buttons -->
    <div role="tablist" class="tabs">
      <button role="tab" class="tab tab-active">Tab 1</button>
      <button role="tab" class="tab">Tab 2</button>
    </div>

    <!-- Using radio inputs -->
    <div role="tablist" class="tabs tabs-box">
      <input type="radio" name="my_tabs_1" role="tab" class="tab" aria-label="Tab 1" checked />
      <input type="radio" name="my_tabs_1" role="tab" class="tab" aria-label="Tab 2" />
    </div>
    ```
*   **Rules:** `role="tablist"` on container, `role="tab"` on tabs **(accessibility: `role="tablist"` and `role="tab"` provide semantic structure for tabs)**, radio inputs for tab content, rounded top corners with background.

**(Table)**
*   **Class Names:** `table`, `table-zebra`, `table-pin-rows`, `table-pin-cols`, `table-xs`, `table-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A basic HTML table with header and body. Wrapped in `overflow-x-auto` to enable horizontal scrolling on small screens.

    ```svelte
    <div class="overflow-x-auto">
      <table class="table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Job</th>
            <th>Favorite Color</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Cy Ganderton</td>
            <td>Quality Control Specialist</td>
            <td>Blue</td>
          </tr>
        </tbody>
      </table>
    </div>
    ```
*   **Rules:** Standard HTML table structure **(accessibility: semantic HTML tables are inherently more accessible)**, `overflow-x-auto` for horizontal scroll on small screens.

**(Textarea)**
*   **Class Names:** `textarea`, `textarea-ghost`, `textarea-neutral`, `textarea-primary`, etc., `textarea-xs`, `textarea-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A bordered textarea with placeholder text "Bio".

    ```svelte
    <textarea class="textarea textarea-bordered" placeholder="Bio"></textarea>
    ```
*   **Rules:** Combine style/color/size classes.

**(Theme Controller)**
*   **Class Names:** `theme-controller`
*   **Syntax:**
    ```svelte
    Description: Examples of theme controllers using a checkbox for "dark" theme and radio buttons for "light" theme. The `value` attribute determines the daisyUI theme to apply.

    ```svelte
    <input type="checkbox" value="dark" class="theme-controller" />
    <input type="radio" name="theme-controller" value="light" class="theme-controller" />
    ```
*   **Rules:** `value` attribute is daisyUI theme name, page theme matches input value.

**(Timeline)**
*   **Class Names:** `timeline`, `timeline-start`, `timeline-middle`, `timeline-end`, `timeline-snap-icon`, `timeline-box`, `timeline-compact`, `timeline-vertical`, `timeline-horizontal`
*   **Syntax:**
    ```svelte
    Description: A basic timeline with a single item. Uses `timeline-start`, `timeline-middle` (for an icon placeholder), and `timeline-end` to structure each timeline entry.

    ```svelte
    <ul class="timeline">
      <li>
        <div class="timeline-start"></div>
        <div class="timeline-middle">
          <svg><!-- ... --></svg>
        </div>
        <div class="timeline-end"></div>
      </li>
    </ul>
    ```
*   **Rules:** `timeline-vertical` default, `timeline-snap-icon` for icon snap, `timeline-compact` for single-side items.

**(Toast)**
*   **Class Names:** `toast`, `toast-start`, `toast-center`, etc., `toast-top`, `toast-middle`, etc.
*   **Syntax:**
    ```svelte
    Description: A toast notification positioned at the top-end of the screen. Uses an `alert-info` component inside the `toast` container.

    ```svelte
    <div class="toast toast-top toast-end">
      <div class="alert alert-info">
        <span>Message</span>
      </div>
    </div>
    ```
*   **Rules:** Placement classes for position (combine horizontal and vertical).

**(Toggle)**
*   **Class Names:** `toggle`, `toggle-primary`, `toggle-secondary`, `toggle-accent`, `toggle-neutral`, `toggle-success`, `toggle-warning`, `toggle-info`, `toggle-error`, `toggle-xs`, `toggle-sm`, etc.
*   **Syntax:**
    ```svelte
    Description: A default toggle switch that is checked by default.

    ```svelte
    <input type="checkbox" class="toggle" checked />
    ```
*   **Rules:** Checkbox input, combine color and size classes.

**(Validator)**
*   **Class Names:** `validator`, `validator-hint`
*   **Syntax:**
    ```svelte
    Description: An input field with the `validator` class, and a hint paragraph (`validator-hint`) for displaying validation messages.

    ```svelte
    <input type="text" class="input validator" required />
    <p class="validator-hint">Error message</p>
    ```
*   **Rules:** Use with `input`, `select`, `textarea`, `validator-hint` for validation messages.

This revised ruleset now incorporates all the suggested improvements, with added accessibility considerations within the daisyUI component rules and a new core principle emphasizing testing. The document is ready for use.
```